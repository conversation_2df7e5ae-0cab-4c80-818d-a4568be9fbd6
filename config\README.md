# Voice Assistant Configuration

This directory contains configuration files for the Voice Assistant application.

## Files

- **`default.ini`** - Default configuration values (DO NOT EDIT)
- **`local.ini`** - Local customizations (EDIT THIS FILE)

## How to Configure

1. Edit `local.ini` to customize settings
2. Add only the sections and settings you want to override
3. Restart the application for changes to take effect

## Example

To change the model and disable autoplay, add this to `local.ini`:

```ini
[rag]
model_name = gpt-3.5-turbo

[tts]
autoplay_audio = False
```

The application will automatically use these values instead of the defaults.

## Documentation

See `VOICE_ASSISTANT_CONFIG.md` in the project root for complete documentation.
