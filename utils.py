import os
import re
import io
import time
import cv2
import pytesseract
import requests
import json
import base64
import numpy as np
from datetime import datetime
from dotenv import load_dotenv
from PIL import Image
from io import BytesIO
import pyautogui
from typing import Optional, Tuple, Any

from langchain_community.chat_models import ChatLiteLLM
from langchain.tools import Tool

from agent.agents import JSONFormattingChain, ValidationChain, InterventionChain, FinalValidationChain
from app.eraignite import EINav

load_dotenv(override=True)



def create_tts_response(text, voice="nova", model="gpt-4o-mini-tts"):
    base_url = os.getenv("OPENAI_API_BASE", "https://api.openai.com")
    url = f"{base_url}/v1/audio/speech"
    headers = {
        "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}",
        "Content-Type": "application/json"
    }
    data = {
        "model": model,
        "input": text,
        "voice": voice
    }
    response = requests.post(url, headers=headers, json=data)
    if response.status_code != 200:
        print(f"Error: {response.status_code} {response.text}")
        return b""
    
    return response.content  # This is the raw MP3 bytes!

def get_transcribe_audio(audio_file_path, model="gpt-4o-transcribe", language=None, prompt=None, temperature=0.0):
    """
    Transcribe audio file with improved accuracy settings.

    Args:
        audio_file_path: Path to the audio file
        model: Model to use for transcription (default: gpt-4o-transcribe)
        language: ISO-639-1 language code (e.g., 'en' for English) to improve accuracy
        prompt: Optional text to guide the model's style or continue a previous audio segment
        temperature: Controls randomness (0.0 = most deterministic, 1.0 = most random)

    Returns:
        Transcribed text or error message
    """
    base_url = os.getenv("OPENAI_API_BASE", "https://api.openai.com")
    url = f"{base_url}/v1/audio/transcriptions"
    headers = {
        "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}",
    }

    with open(audio_file_path, "rb") as audio_file:
        files = {"file": (os.path.basename(audio_file_path), audio_file, "audio/wav")}
        data = {
            "model": model,
            "response_format": "json",  # Explicitly request JSON format
            "temperature": temperature  # Lower temperature for more consistent results
        }

        # Add language parameter if specified (helps with accuracy)
        if language:
            data["language"] = language

        # Add prompt if specified (helps with context and terminology)
        if prompt:
            data["prompt"] = prompt

        response = requests.post(url, headers=headers, files=files, data=data)

    if response.status_code != 200:
        return f"Error: {response.status_code} {response.text}"

    result = response.json()
    transcribed_text = result.get("text", "").strip()

    # Check if the transcription is just returning the prompt (indicates no clear speech detected)
    if prompt and transcribed_text == prompt.strip():
        return ""  # Return empty string to indicate no speech detected

    return transcribed_text




def index_exists(index_path: str) -> bool:
    return (
        os.path.exists(os.path.join(index_path, "index.faiss")) and
        os.path.exists(os.path.join(index_path, "index.pkl"))
    )

def format_date_from_day(day_input):
    """
    Format a day input into MM/DD/YY format using current month and year.

    Args:
        day_input: Can be a day number (1-31) as string or int, or a full date string

    Returns:
        Formatted date string in MM/DD/YY format

    Raises:
        ValueError: If day is invalid or date is outside current month
    """
    today = datetime.today()
    current_month = today.month
    current_year = today.year

    # If input is just a day number
    try:
        day = int(str(day_input).strip())
        if 1 <= day <= 31:
            try:
                # Create date with current month/year and provided day
                date_obj = datetime(current_year, current_month, day)
                return date_obj.strftime("%m/%d/%y")
            except ValueError:
                raise ValueError(f"Invalid day {day} for {current_month}/{current_year}")
    except ValueError:
        pass

    # If input might be a full date, try to parse it and validate it's in current month
    return format_date(str(day_input))

def format_date(date_str):
    """
    Parse various date formats and validate they're in the current month.

    Args:
        date_str: Date string in various formats

    Returns:
        Formatted date string in MM/DD/YY format

    Raises:
        ValueError: If date cannot be parsed or is outside current month
    """
    today_month = datetime.today().month
    today_year = datetime.today().year
    formats = [
        "%m/%d/%y",
        "%d/%m/%y",
        "%m/%d/%Y",
        "%y/%m/%d",
        "%y/%d/%m",
        "%Y/%m/%d",
        "%d/%m/%Y",
        "%d%m%y",
        "%m%d%y",
        "%y%d%m",
        "%y%m%d",
        "%Y-%m-%d",
        "%d-%m-%Y",
        "%m-%d-%Y",
        "%d-%m-%y",
        "%m-%d-%y",
        "%d.%m.%Y",
        "%d.%m.%y",
        "%Y.%m.%d",
        "%B %d, %Y",
        "%d %B %Y",
        "%B %d %Y",
        "%d %B, %Y",
        "%b %d, %Y",
        "%d %b %Y",
        "%b %d %Y",
        "%d %b, %Y",
        "%B %d, %y",
        "%d %B %y",
        "%b %d, %y",
        "%d %b %y",
    ]

    for fmt in formats:
        try:
            parsed = datetime.strptime(date_str, fmt)
            if parsed.month == today_month and parsed.year == today_year:
                return parsed.strftime("%m/%d/%y")
        except ValueError:
            continue

    raise ValueError(f"Could not parse date or date is not in current month ({today_month}/{today_year}): {date_str}")

def validate_current_month_day(day_input):
    """
    Validate that a day input is valid for the current month.

    Args:
        day_input: Day number or date string

    Returns:
        tuple: (is_valid: bool, formatted_date: str, error_message: str)
    """
    try:
        formatted_date = format_date_from_day(day_input)
        return True, formatted_date, ""
    except ValueError as e:
        current_month = datetime.today().strftime("%B")
        current_year = datetime.today().year
        return False, "", f"Invalid date. Please provide a day number (1-31) for {current_month} {current_year}. Error: {str(e)}"

def clean_reference_number(reference_input):
    """
    Clean reference number to contain only alphanumeric characters.

    Removes spaces, dashes, slashes, and any other non-alphanumeric characters.

    Args:
        reference_input: Reference number string that may contain special characters

    Returns:
        str: Clean alphanumeric string

    Examples:
        "06 18 2025 CA" -> "06182025CA"
        "06-18-2025CA" -> "06182025CA"
        "06/18/2025CA" -> "06182025CA"
        "REF#123!" -> "REF123"
    """
    if not reference_input:
        return ""

    # Convert to string and remove all non-alphanumeric characters
    cleaned = re.sub(r'[^a-zA-Z0-9]', '', str(reference_input))
    return cleaned

def validate_and_extract_cash_receipt_data(inputs, form_data=""):
    """
    Intelligent validation that extracts missing information from conversation history.
    Returns either complete data ready for form submission or validation errors.
    """
    conversation_history = inputs.get('conversation_history','')
    chain_inputs = {'input': form_data, 'conversation_history': conversation_history, 'current_date': datetime.today().strftime("%m/%d/%y")}
    llm = ChatLiteLLM(model_name="gpt-4o")
    validation_chain = ValidationChain.from_llm(llm)
    validation_output = validation_chain.invoke(chain_inputs)
    validation_output = validation_output.get("text", {})

    # Clean and parse the validation result
    cleaned = re.sub(r"^```(?:json)?\s*", "", validation_output.strip())
    cleaned = re.sub(r"\s*```$", "", cleaned)

    # Try to extract JSON from the response if it contains extra text
    json_match = re.search(r'\{.*\}', cleaned, re.DOTALL)
    if json_match:
        cleaned = json_match.group(0)

    try:
        validation_result = json.loads(cleaned)
        return validation_result
    except json.JSONDecodeError as e:
        print(f"Failed to parse validation result: {cleaned}")
        print(f"Error: {str(e)}")
        # Return error result if parsing fails
        return {
            "is_complete": False,
            "missing_fields": ["validation_error"],
            "validation_errors": [f"Failed to parse validation result: {str(e)}"],
            "next_question": "Please provide all required information for the cash receipt."
        }


# Keep the old function name for backward compatibility
def validate_cash_receipt_data(inputs, form_data=""):
    """Legacy function name - calls the new intelligent validation function"""
    return validate_and_extract_cash_receipt_data(inputs, form_data)


def intervention_agent(inputs, context):
    """
    Handle user deviations during questionnaire flow using intervention agent.
    Returns extracted answer or guidance to get back on track.
    """
    conversation_history = inputs.get('conversation_history', '')
    intervention_context = context.get('intervention_context', {})

    # Add conversation history to the context
    intervention_context['conversation_history'] = conversation_history

    llm = ChatLiteLLM(model_name="gpt-4o")
    intervention_chain = InterventionChain.from_llm(llm)
    intervention_output = intervention_chain.invoke(intervention_context)
    intervention_response = intervention_output.get("text", "")

    return intervention_response.strip()


def final_validation_agent(inputs, context):
    """
    Perform final validation and form submission using final validation agent.
    Returns either missing field questions or calls fill_the_form_tool.
    """
    conversation_history = inputs.get('conversation_history', '')
    validation_context = context.get('validation_context', {})

    # Prepare the input for the prompt template
    prompt_input = {
        'collected_answers': validation_context.get('collected_answers', '{}'),
        'conversation_history': validation_context.get('conversation_history', conversation_history),
        'current_date': validation_context.get('current_date', '')
    }

    llm = ChatLiteLLM(model_name="gpt-4o")
    final_validation_chain = FinalValidationChain.from_llm(llm)
    validation_output = final_validation_chain.invoke(prompt_input)
    validation_response = validation_output.get("text", "").strip()

    # Check if the validation agent returned complete form data
    if validation_response.startswith("FORM_COMPLETE:"):
        # Extract the JSON data and call the form tool
        json_data = validation_response.replace("FORM_COMPLETE:", "").strip()
        try:
            # Call the form tool directly with the complete data
            fill_the_form_tool_with_query(inputs, json_data)
            return "Form filled successfully. Your cash receipt has been processed and added to the system."
        except Exception as e:
            return f"Error processing form: {str(e)}"
    else:
        # Return the validation response (likely a question for missing data)
        return validation_response


def format_form_info_into_json(inputs, form_data):
    conversation_history = inputs.get('conversation_history','')
    chain_inputs = {'input': form_data, 'conversation_history': conversation_history}
    llm = ChatLiteLLM(model_name="gpt-4o")
    fomatting_chain = JSONFormattingChain.from_llm(llm)
    formatting_output = fomatting_chain.invoke(chain_inputs)
    formatting_output = formatting_output.get("text", {})
    cleaned = re.sub(r"^```(?:json)?\s*", "", formatting_output.strip())
    cleaned = re.sub(r"\s*```$", "", cleaned)
    final_output = json.loads(cleaned)

    # Format dates using the new day-based formatting
    if final_output.get('deposit_date'):
        try:
            final_output['deposit_date'] = format_date_from_day(final_output['deposit_date'])
        except ValueError as e:
            print(f"Warning: Could not format deposit_date '{final_output['deposit_date']}': {e}")
            final_output['deposit_date'] = ""

    if final_output.get('receipt_date'):
        try:
            final_output['receipt_date'] = format_date_from_day(final_output['receipt_date'])
        except ValueError as e:
            print(f"Warning: Could not format receipt_date '{final_output['receipt_date']}': {e}")
            final_output['receipt_date'] = ""

    # Clean reference_number to be alphanumeric only
    if final_output.get('reference_number'):
        final_output['reference_number'] = clean_reference_number(final_output['reference_number'])
    else:
        # Generate default reference number: MMDDYYYY + first two letters of pay_code
        today = datetime.today()
        date_part = today.strftime("%m%d%Y")  # MMDDYYYY format

        # Get first two letters of pay_code in uppercase
        pay_code = final_output.get('pay_code', '')
        if pay_code and len(pay_code) >= 2:
            pay_code_prefix = pay_code[:2].upper()
        else:
            pay_code_prefix = "XX"  # Default if pay_code is missing or too short

        final_output['reference_number'] = date_part + pay_code_prefix

    final_output = {k: str(v) for k, v in final_output.items()}
    return final_output

def fill_the_form_tool_with_query(inputs={}, query=""):
    """
    Fill the form tool that receives form data from the LLM query parameter
    """
    try:
        # Parse the query as JSON form data
        if query.strip():
            # Try to parse as JSON
            try:
                form_data = json.loads(query)
            except json.JSONDecodeError:
                # If not valid JSON, try to extract JSON from the query
                import re
                json_match = re.search(r'\{.*\}', query, re.DOTALL)
                if json_match:
                    form_data = json.loads(json_match.group())
                else:
                    # Fallback: use empty form data
                    form_data = {}
        else:
            form_data = {}

        # Call the original form filling function
        return fill_the_form_tool(inputs, form_data)
    except Exception as e:
        return f"Error processing form data: {str(e)}"

def fill_the_form_tool(inputs={}, form_data=None):
    # Use provided form_data if available, otherwise try to parse from query or use defaults
    if form_data:
        form_info = form_data
    else:
        # form_info = format_form_info_into_json(inputs, form_data)
        form_info = {}
    ERA_SCREEN = EINav()
    ERA_SCREEN.show_window()
    time.sleep(2)
    # ERA_SCREEN.keypress(inputs.get('application_hotkey', ['alt','a','enter']))
    # time.sleep(20)
    ERA_SCREEN.keypress(inputs.get('form_hotkey', ['alt','u','enter']))
    time.sleep(5)
    ERA_SCREEN.keypress(['f9'])
    time.sleep(2)
    ERA_SCREEN.type(form_info['reference_number'])
    ERA_SCREEN.keypress(['tab'])
    time.sleep(1)
    if form_info.get('new_deposit', True):
        ERA_SCREEN.type(form_info['bank_code'])
        ERA_SCREEN.keypress(['tab'])
        time.sleep(1)
        ERA_SCREEN.type(form_info['deposit_date'])
        ERA_SCREEN.keypress(['tab'])
        time.sleep(1)
    ERA_SCREEN.keypress(['tab'])
    time.sleep(1)
    ERA_SCREEN.type(form_info['receipt_date'])
    ERA_SCREEN.keypress(['tab'])
    time.sleep(1)
    ERA_SCREEN.type(form_info['customer_number'])
    ERA_SCREEN.keypress(['tab'])
    time.sleep(1)
    ERA_SCREEN.type(form_info['employee_number'])
    ERA_SCREEN.keypress(['tab'])
    time.sleep(1)
    ERA_SCREEN.keypress([form_info['pay_code'][0]])
    time.sleep(1)
    ERA_SCREEN.keypress(['enter'])
    time.sleep(1)
    ERA_SCREEN.type(str(form_info['amount']) +'00')
    ERA_SCREEN.keypress(['tab'])
    time.sleep(1)
    ERA_SCREEN.type(form_info['account_number'])
    ERA_SCREEN.keypress(['f12'])
    time.sleep(1)
    ERA_SCREEN.type(str(form_info['amount_in_100_bills'])+'00')
    ERA_SCREEN.keypress(['f12'])
    time.sleep(1)
    ERA_SCREEN.keypress(['f12'])
    return "Receipt has been added successfully"

def validate_date_tool(day_input): 
    """
    Tool for validating date input during conversation.

    Args:
        day_input: Day number or date string to validate

    Returns:
        Validation result message
    """
    is_valid, formatted_date, error_message = validate_current_month_day(day_input)

    if is_valid:
        current_month = datetime.today().strftime("%B")
        return f"✅ Valid date: {formatted_date} ({current_month} {day_input})"
    else:
        return f"❌ {error_message}"

def get_tools(inputs):
    tools = [
        Tool(
            name="validate_and_extract_cash_receipt_data",
            description="Intelligently validate and extract all required cash receipt information from conversation history. Returns either complete data ready for form submission or specific missing fields that need LLM intervention.",
            func=lambda query: validate_and_extract_cash_receipt_data(inputs, query)
        ),
        Tool(
            name="validate_cash_receipt_data",
            description="Legacy validation tool - use validate_and_extract_cash_receipt_data instead for better intelligence.",
            func=lambda query: validate_cash_receipt_data(inputs, query)
        ),
        Tool(
            name="format_cash_receipt_data",
            description="Format the collected cash receipt information into JSON format. Only call this AFTER validation confirms all data is complete.",
            func=lambda query: format_form_info_into_json(inputs, query)
        ),
        Tool(
            name="fill_the_form_tool",
            description="Filling the Print_Cash_Receipt form given the new receipt info. Pass the complete form data as JSON string in the query parameter.",
            func=lambda query: fill_the_form_tool_with_query(inputs, query)
        ),
        Tool(
            name="validate_date",
            description="Validate that a day number is valid for the current month. Use this when user provides a date to ensure it's valid before proceeding.",
            func=validate_date_tool
        ),
        Tool(
            name="intervention_agent",
            description="Handle user deviations during questionnaire flow. Use when user doesn't provide expected answer or asks questions. Returns extracted answer or guidance.",
            func=lambda context: intervention_agent(inputs, context)
        ),
        Tool(
            name="final_validation_agent",
            description="Perform final validation of all collected information and submit form when complete. Use after all questionnaire questions are answered.",
            func=lambda context: final_validation_agent(inputs, context)
        ),
    ]
    return tools

# if __name__ == "__main__":
#     form_data = 'new_deposit":true,"reference_number":"060325CA","bank_code":"1","deposit_date":"06/01/25","receipt_date":"06/01/25","customer_number":"1","employee_number":"1","pay_code":"CA-CASH","amount":200,"account_number":"3000","no_of_100_bills":"200"}'
#     inputs = {'application_hotkey': ['alt','a','enter'], 'form_hotkey': ['alt','u','enter']}
#     fill_the_form_tool(inputs. form_data)