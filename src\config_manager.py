"""
Voice Assistant Configuration Manager

Handles loading and validation of configuration from INI files.
"""

import os
import configparser
from pathlib import Path
from typing import Dict, Any


class VoiceAssistantConfig:
    """Configuration manager for Voice Assistant with INI file support"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.default_config_path = self.config_dir / "default.ini"
        self.local_config_path = self.config_dir / "local.ini"
        
        # Initialize configuration parser
        self.config = configparser.ConfigParser()
        
        # Load configurations
        self._load_configurations()
        
        # Parse all configuration values
        self._parse_config_values()
    
    def _load_configurations(self):
        """Load default and local configuration files"""
        # Load default configuration first
        if self.default_config_path.exists():
            self.config.read(self.default_config_path, encoding='utf-8')
        
        # Load local configuration (automatically overrides defaults)
        if self.local_config_path.exists():
            self.config.read(self.local_config_path, encoding='utf-8')
    
    def _parse_config_values(self):
        """Parse configuration values from the loaded config"""
        # RAG Configuration
        self.rag_prefix = self.config.get("rag", "prefix", fallback="Which application should be used for the following inquiry:\n")
        self.forms_path = self.config.get("rag", "forms_path", fallback="forms.json")
        self.file_path = self.config.get("rag", "file_path", fallback="ZI102.pdf")
        self.index_path = self.config.get("rag", "index_path", fallback="rag_index")
        self.model_name = self.config.get("rag", "model_name", fallback="gpt-4o")
        
        # Audio Configuration
        self.audio_rate = self.config.getint("audio", "rate", fallback=16000)
        self.pause_threshold = self.config.getfloat("audio", "pause_threshold", fallback=1.0)

        # Streaming Configuration
        self.silence_threshold = self.config.getfloat("streaming", "silence_threshold", fallback=0.01)
        self.max_silence_duration = self.config.getfloat("streaming", "max_silence_duration", fallback=2.0)
        self.min_audio_duration = self.config.getfloat("streaming", "min_audio_duration", fallback=1.0)
        self.feedback_prevention_delay = self.config.getfloat("streaming", "feedback_prevention_delay", fallback=3.0)

        # Transcription Configuration (English only)
        self.transcription_model = self.config.get("transcription", "model", fallback="gpt-4o-transcribe")
        self.transcription_language = "en"  # Always English
        self.transcription_temperature = self.config.getfloat("transcription", "temperature", fallback=0.0)
        self.transcription_prompt = self.config.get("transcription", "prompt", fallback="This is an English voice assistant conversation about business applications, forms, and data entry. Common terms include invoice, receipt, payment, cash, check, deposit, customer, vendor, account, form, application.")
        
        # UI Configuration
        self.page_title = self.config.get("ui", "page_title", fallback="ERA-IGNITE Voice Assistant")
        self.page_icon = "🎙️"  # Hardcoded emoji to avoid encoding issues
        self.layout = self.config.get("ui", "layout", fallback="wide")
        
        # Application Configuration
        self.default_application = self.config.get("application", "default_application", fallback="Accounting")
        self.default_form = self.config.get("application", "default_form", fallback="PRINT_CASH_RECEIPTS")
        
        # Agent Configuration
        self.agent_verbose = self.config.getboolean("agent", "verbose", fallback=True)
        
        # TTS Configuration
        self.enable_tts = self.config.getboolean("tts", "enable_tts", fallback=True)
        self.autoplay_audio = self.config.getboolean("tts", "autoplay_audio", fallback=True)
        
        # Session Configuration
        self.enable_session_reset = self.config.getboolean("session", "enable_reset", fallback=True)
    
    def validate_config(self) -> Dict[str, str]:
        """Validate configuration and return any issues"""
        issues = {}
        
        # Check file paths
        if not os.path.exists(self.forms_path):
            issues["forms_path"] = f"Forms file not found: {self.forms_path}"
        
        if not os.path.exists(self.file_path):
            issues["file_path"] = f"Document file not found: {self.file_path}"
        
        # Check audio rate
        if self.audio_rate < 8000 or self.audio_rate > 48000:
            issues["audio_rate"] = f"Audio rate should be between 8000-48000 Hz, got: {self.audio_rate}"
        
        # Check pause threshold
        if self.pause_threshold < 0.1 or self.pause_threshold > 10.0:
            issues["pause_threshold"] = f"Pause threshold should be between 0.1-10.0 seconds, got: {self.pause_threshold}"

        # Check streaming parameters
        if self.silence_threshold < 0.001 or self.silence_threshold > 1.0:
            issues["silence_threshold"] = f"Silence threshold should be between 0.001-1.0, got: {self.silence_threshold}"

        if self.max_silence_duration < 0.5 or self.max_silence_duration > 10.0:
            issues["max_silence_duration"] = f"Max silence duration should be between 0.5-10.0 seconds, got: {self.max_silence_duration}"

        if self.min_audio_duration < 0.1 or self.min_audio_duration > 5.0:
            issues["min_audio_duration"] = f"Min audio duration should be between 0.1-5.0 seconds, got: {self.min_audio_duration}"

        # Check layout
        if self.layout not in ["wide", "centered"]:
            issues["layout"] = f"Layout must be 'wide' or 'centered', got: {self.layout}"
        
        return issues
    
    def get_config_info(self) -> Dict[str, Any]:
        """Get configuration file information"""
        return {
            "config_dir": str(self.config_dir),
            "default_config": str(self.default_config_path),
            "local_config": str(self.local_config_path),
            "default_exists": self.default_config_path.exists(),
            "local_exists": self.local_config_path.exists()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for display"""
        return {
            "rag_prefix": self.rag_prefix,
            "forms_path": self.forms_path,
            "file_path": self.file_path,
            "index_path": self.index_path,
            "model_name": self.model_name,
            "audio_rate": self.audio_rate,
            "pause_threshold": self.pause_threshold,
            "silence_threshold": self.silence_threshold,
            "max_silence_duration": self.max_silence_duration,
            "min_audio_duration": self.min_audio_duration,
            "transcription_model": self.transcription_model,
            "transcription_language": self.transcription_language,
            "transcription_temperature": self.transcription_temperature,
            "transcription_prompt": self.transcription_prompt,
            "page_title": self.page_title,
            "page_icon": self.page_icon,
            "layout": self.layout,
            "default_application": self.default_application,
            "default_form": self.default_form,
            "agent_verbose": self.agent_verbose,
            "enable_tts": self.enable_tts,
            "autoplay_audio": self.autoplay_audio,
            "enable_session_reset": self.enable_session_reset
        }
