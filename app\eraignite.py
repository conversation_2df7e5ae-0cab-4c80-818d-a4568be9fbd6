import time
import base64
import pyautogui
import win32gui
import win32con
import win32process
import win32api
import ctypes
import pyperclip
import pygetwindow as gw
from io import BytesIO
from PIL import ImageGrab


class EINav:
    def get_environment(self):
        return "windows"

    def get_dimensions(self):
        return pyautogui.size()

    def __init__(self):
        self.dimensions = self.get_dimensions()
        title_substring = "(ACC) - "
        matches = gw.getWindowsWithTitle(title_substring)
        if not matches:
            raise RuntimeError(f"No window with title containing '{title_substring}' found.")

        window = matches[0]
        self.hwnd = window._hWnd

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    def show_window(self):
        # Get current foreground window and thread
        fg_win = win32gui.GetForegroundWindow()
        fg_thread_id, _ = win32process.GetWindowThreadProcessId(fg_win)
        target_thread_id, _ = win32process.GetWindowThreadProcessId(self.hwnd)
        current_thread_id = win32api.GetCurrentThreadId()

        # Attach threads to bypass foreground lock
        if fg_thread_id != target_thread_id:
            ctypes.windll.user32.AttachThreadInput(fg_thread_id, current_thread_id, True)
            ctypes.windll.user32.AttachThreadInput(target_thread_id, current_thread_id, True)

        # Restore and set foreground
        win32gui.ShowWindow(self.hwnd, win32con.SW_RESTORE)
        time.sleep(0.05)
        win32gui.SetForegroundWindow(self.hwnd)
        win32gui.SetFocus(self.hwnd)

        # Detach threads
        if fg_thread_id != target_thread_id:
            ctypes.windll.user32.AttachThreadInput(fg_thread_id, current_thread_id, False)
            ctypes.windll.user32.AttachThreadInput(target_thread_id, current_thread_id, False)

    def screenshot(self) -> str:
        img = ImageGrab.grab()
        buffered = BytesIO()
        img.save(buffered, format="PNG")
        b64_img = base64.b64encode(buffered.getvalue()).decode("utf-8")
        return b64_img

    def click(self, x: int, y: int, button: str = "left") -> None:
        pyautogui.click(x, y, button=button)

    def double_click(self, x: int, y: int) -> None:
        pyautogui.doubleClick(x, y)

    def scroll(self, x: int, y: int, scroll_x: int, scroll_y: int) -> None:
        pyautogui.moveTo(x, y)
        pyautogui.scroll(scroll_y)

    def type(self, text: str) -> None:
        pyperclip.copy(text)
        pyautogui.hotkey("ctrl", "v")

    def wait(self, ms: int = 1000) -> None:
        time.sleep(ms / 1000)

    def move(self, x: int, y: int) -> None:
        pyautogui.moveTo(x, y)

    def keypress(self, keys: list[str]) -> None:
        mapping = {
            "ENTER": "enter",
            "LEFT": "left",
            "RIGHT": "right",
            "UP": "up",
            "DOWN": "down",
            "ESC": "esc",
            "SPACE": "space",
            "BACKSPACE": "backspace",
            "TAB": "tab",
            "ALT": "alt",
        }

        # Normalize keys
        mapped_keys = [mapping.get(k.upper(), k.lower()) for k in keys]

        # Check for trailing "enter"
        press_enter = False
        if len(mapped_keys) > 1 and mapped_keys[-1] == "enter":
            press_enter = True
            mapped_keys = mapped_keys[:-1]  # Remove enter from hotkey combo

        # Press Alt + combo
        if mapped_keys:
            pyautogui.hotkey(*mapped_keys)
            time.sleep(0.1)

        # Then press enter if needed
        if press_enter:
            pyautogui.press("enter")

    def drag(self, path: list[dict[str, int]]) -> None:
        if not path:
            return
        pyautogui.moveTo(path[0]["x"], path[0]["y"])
        pyautogui.mouseDown()
        for point in path[1:]:
            pyautogui.moveTo(point["x"], point["y"])
        pyautogui.mouseUp()

    def get_current_url(self):
        return None  # Not applicable unless browser automation is implemented

if __name__ == "__main__":
    win = EINav()
    win.show_window()
    win.keypress(['alt','a','enter'])
    time.sleep(15)
    win.keypress(['alt','u','enter'])
    time.sleep(10)
    win.keypress(['f9'])
    time.sleep(2)
    win.type('060525CA')
    # win.no_top_most_window()
    # win.keypress(['alt','a','enter'])