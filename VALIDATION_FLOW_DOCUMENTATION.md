# Questionnaire-Based Cash Receipt Flow Documentation

## Overview

This document describes the questionnaire-based cash receipt flow that uses predefined questions with minimal LLM intervention. The system operates primarily through a structured questionnaire with two specific LLM agents that activate only when needed:

1. **LLM Intervention Agent** (`LLM_INTERVENTION_AGENT`) - Handles user deviations from questionnaire flow
2. **Final Validation Agent** (`FINAL_VALIDATION_AGENT`) - Validates all collected data after questionnaire completion

## Questionnaire-Based Process Flow

### 1. Predefined Questionnaire Phase
- The system follows a structured questionnaire with 10 predefined questions
- Each question has specific validation rules and expected answer types
- No LLM is used during normal questionnaire flow - responses are validated using rule-based logic
- Users are guided through questions sequentially

### 2. LLM Intervention Phase (When Needed)
When users deviate from expected answers or ask questions, the **LLM Intervention Agent** activates:

#### LLM Intervention Agent (`LLM_INTERVENTION_AGENT`)
- **Purpose**: Handle user deviations, clarifications, or unexpected responses
- **Trigger**: When user doesn't provide expected answer format or asks questions
- **Goal**: Extract the intended answer and return to questionnaire flow
- **Prompt**: Uses `LLM_INTERVENTION_AGENT` prompt template

### 3. Final Validation Phase (After Questionnaire Completion)
When all questionnaire questions are completed, the **Final Validation Agent** activates:

#### Final Validation Agent (`FINAL_VALIDATION_AGENT`)
- **Purpose**: Validate all collected information and check for missing values
- **Trigger**: After all 10 questionnaire questions are answered
- **Intelligence**: Reviews conversation history to auto-fix missing information when possible
- **Goal**: Either submit the form with complete data OR request specific missing information
- **Prompt**: Uses `FINAL_VALIDATION_AGENT` prompt template

#### Two Possible Outcomes:

**Complete Data Available - Form Submission**:
- All required fields are present and valid
- Agent calls `fill_the_form_tool` directly
- User receives success confirmation
- Process completes

**Missing Information - Specific Request**:
- Agent identifies specific missing or invalid fields
- Provides targeted questions for missing data
- Returns to questionnaire flow for missing items
- Re-validates until complete

## System Architecture

### Questionnaire Handler
- **Primary Mode**: Rule-based questionnaire processing
- **10 Predefined Questions**: Each with specific validation rules
- **Sequential Flow**: Users progress through questions in order
- **Minimal LLM Usage**: Only activates LLM agents when necessary

### LLM Agent Integration
The system uses two specialized LLM agents:

#### 1. Intervention Agent (`LLM_INTERVENTION_AGENT`)
**Activation Triggers**:
- User provides unexpected answer format
- User asks clarifying questions
- User deviates from questionnaire flow
- Answer doesn't match expected data type

**Behavior**:
- Attempts to extract intended answer from user input
- Provides clarification or guidance
- Returns extracted answer to continue questionnaire
- Falls back to questionnaire flow once answer is obtained

#### 2. Final Validation Agent (`FINAL_VALIDATION_AGENT`)
**Activation Trigger**:
- All 10 questionnaire questions have been answered

**Behavior**:
- Reviews all collected answers for completeness
- Auto-fixes missing information from conversation history when possible
- Validates data types and business rules
- Either submits form directly OR requests specific missing information

## Implementation Details

### Core Components

#### 1. LLM Intervention Agent Prompt (`LLM_INTERVENTION_AGENT`)
```python
# Located in: prompts/accounting.py
LLM_INTERVENTION_AGENT = """
You are an intervention agent for a cash receipt questionnaire system.
Your role is to handle user deviations and extract intended answers.

CONTEXT: User is in a questionnaire flow but provided an unexpected response or asked a question.

YOUR TASKS:
1. Understand what the user is trying to communicate
2. Extract the intended answer if possible
3. Provide clarification if needed
4. Guide user back to questionnaire flow
...
"""
```

#### 2. Final Validation Agent Prompt (`FINAL_VALIDATION_AGENT`)
```python
# Located in: prompts/accounting.py
FINAL_VALIDATION_AGENT = """
You are a final validation agent for cash receipt form submission.
Your role is to validate all collected information and submit the form.

CONTEXT: All questionnaire questions have been answered.

YOUR TASKS:
1. Review all collected answers for completeness and validity
2. Auto-fix missing information from conversation history when possible
3. Either call fill_the_form_tool with complete data OR request specific missing information
...
"""
```

#### 3. Agent Chain Classes
```python
# Located in: agent/agents.py
class InterventionChain(LLMChain):
    """Chain for handling user deviations during questionnaire flow."""

class FinalValidationChain(LLMChain):
    """Chain for final validation and form submission."""
```

#### 4. Agent Functions
```python
# Located in: utils.py
def intervention_agent(inputs, context):
    """Handle user deviations using LLM_INTERVENTION_AGENT prompt"""

def final_validation_agent(inputs, context):
    """Perform final validation using FINAL_VALIDATION_AGENT prompt"""
```

### System Integration

#### 1. WebSocket Voice Server Integration
The voice server integrates both LLM agents:

```python
# LLM Intervention Agent (when user deviates)
if agent_type == "intervention":
    from utils import intervention_agent
    llm_response = intervention_agent(inputs, context)
    # Extract answer and return to questionnaire flow

# Final Validation Agent (after questionnaire completion)
elif agent_type == "final_validation":
    from utils import final_validation_agent
    llm_response = final_validation_agent(inputs, context)
    # Either submit form or request missing information
```

#### 2. Questionnaire Handler Integration
The questionnaire handler determines when to activate LLM agents:

```python
# Check if user response matches expected format
if extracted_answer is not None:
    # Continue with questionnaire flow
    return next_question, False, {}
else:
    # Activate LLM Intervention Agent
    return self._initiate_llm_intervention(user_input, current_question)

# After all questions completed
if self.current_question_index >= len(self.questions):
    # Activate Final Validation Agent
    return self._complete_questionnaire()
```

#### 3. Tool Integration
Both agents have access to the same tools:
- `fill_the_form_tool` - For direct form submission
- `validate_date` - For date validation
- Other utility tools as needed

## System Benefits

### 1. Efficient Questionnaire Flow
- **Structured Process**: Clear 10-question sequence with predictable flow
- **Rule-Based Validation**: Fast, deterministic validation for expected answers
- **Minimal LLM Usage**: Reduces API costs and response times
- **Consistent Experience**: Standardized questions and validation

### 2. Smart LLM Integration
- **Targeted Intervention**: LLM only activates when users deviate or need help
- **Context-Aware Assistance**: Intervention agent understands questionnaire context
- **Intelligent Validation**: Final validation agent auto-fixes missing information
- **Seamless Handover**: Smooth transitions between rule-based and LLM processing

### 3. Superior User Experience
- **Natural Conversation**: Users can ask questions or provide unexpected answers
- **Flexible Input**: System handles various answer formats and deviations
- **Intelligent Guidance**: Specific help when users are confused
- **Efficient Completion**: Direct form submission when all data is complete

### 4. Robust Data Handling
- **Comprehensive Validation**: Final agent ensures all required fields are present
- **Auto-Correction**: Missing information extracted from conversation history
- **Type Safety**: Proper data type validation and conversion
- **Business Rule Compliance**: Ensures data meets form requirements

### 5. Maintainable Architecture
- **Clear Separation**: Distinct roles for questionnaire, intervention, and validation
- **Modular Design**: Each agent has specific, well-defined responsibilities
- **Scalable Pattern**: Easy to add new questionnaires or modify existing ones
- **Testable Components**: Each agent can be tested independently

## Usage Examples

### Normal Questionnaire Flow
1. **User starts**: "I need to print a cash receipt"
2. **System**: Asks question 1: "Are you adding this receipt to an existing bank deposit or creating a new one?"
3. **User**: "Creating a new one" ✅ (Expected answer format)
4. **System**: Continues to question 2 (no LLM needed)
5. **Process continues** through all 10 questions
6. **Final Validation Agent**: Reviews all answers, submits form

### LLM Intervention Example
1. **System**: "What is the bank code?"
2. **User**: "Um, what's a bank code? I'm not sure what you mean" ❓ (Deviation)
3. **LLM Intervention Agent**: Activates to provide clarification
4. **Agent**: "A bank code is the number that identifies which bank account the deposit goes to. Do you know the bank account number or name?"
5. **User**: "Oh, it's account 1"
6. **Agent**: Extracts "1" as bank code, returns to questionnaire flow

### Final Validation Example
1. **All 10 questions completed**
2. **Final Validation Agent**: Reviews conversation history
3. **Agent finds**: All required fields present and valid
4. **Agent**: Calls `fill_the_form_tool` directly
5. **User**: Receives success confirmation

### Missing Information Example
1. **All 10 questions completed**
2. **Final Validation Agent**: Reviews conversation history
3. **Agent finds**: Customer number mentioned as "John" but needs integer ID
4. **Agent**: "I need the customer's numeric ID number. You mentioned 'John' - what is John's customer ID number?"
5. **User**: Provides numeric ID
6. **Agent**: Re-validates and submits form

## Configuration

The system uses two specific prompt templates:
- `LLM_INTERVENTION_AGENT` - For handling user deviations
- `FINAL_VALIDATION_AGENT` - For final validation and submission

Both agents use the same LLM model configuration as the rest of the system and have access to the same tools for form submission and validation.
