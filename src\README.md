# Source Package

This directory contains core modules for the ERA-IGNITE Voice Assistant application.

## Modules

- **`config_manager.py`** - Configuration management system for loading and validating INI-based settings

## Usage

```python
from src.config_manager import VoiceAssistantConfig

# Load configuration
config = VoiceAssistantConfig()

# Access settings
print(config.model_name)
print(config.audio_rate)
```

## Adding New Modules

When adding new core modules to this package:

1. Create the module file in this directory
2. Add the import to `__init__.py`
3. Update the `__all__` list in `__init__.py`
