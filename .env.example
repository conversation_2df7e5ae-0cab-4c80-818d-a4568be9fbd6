# rename this to .env and fill in your own keys

OPENAI_API_KEY = "sk-proj-123"
OPENAI_API_BASE = "org-123"

BROWSERBASE_API_KEY="00000000-0000-0000-0000-000000000000"
BROWSERBASE_PROJECT_ID="bb_live_00000000-00000"

SCRAPYBARA_API_KEY="scrapy-123"

# Voice Assistant Configuration
# Copy these values to your .env file and modify as needed

# RAG Configuration
RAG_PREFIX="Which application should be used for the following inquiry:\n"
FORMS_PATH="forms.json"
FILE_PATH="ZI102.pdf"
INDEX_PATH="rag_index"
MODEL_NAME="gpt-4o"

# Audio Configuration
AUDIO_RATE=16000
PAUSE_THRESHOLD=1.0

# UI Configuration
PAGE_TITLE="ERA-IGNITE Voice Assistant"
PAGE_ICON="🎙️"
PAGE_LAYOUT="wide"

# Application Configuration
DEFAULT_APPLICATION="Accounting"
DEFAULT_FORM="PRINT_CASH_RECEIPTS"

# Agent Configuration
AGENT_VERBOSE=True

# TTS Configuration
ENABLE_TTS=True
AUTOPLAY_AUDIO=True

# Session Configuration
ENABLE_SESSION_RESET=True