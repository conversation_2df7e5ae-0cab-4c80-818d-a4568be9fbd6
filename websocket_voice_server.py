"""
WebSocket Voice Server for Real-time Audio Streaming

This server handles real-time audio streaming and voice processing.
"""

import asyncio
import websockets
import json
import base64
import tempfile
import wave
import numpy as np
from io import BytesIO
import logging
import re

from utils import get_transcribe_audio, create_tts_response, index_exists
from src.config_manager import VoiceAssistantConfig
from agent.agents import RAGChain
from questionnaire_handler import QuestionnaireHandler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceStreamProcessor:
    def __init__(self):
        self.config = VoiceAssistantConfig()
        self.audio_buffer = []
        self.is_recording = False
        self.silence_threshold = self.config.silence_threshold
        self.silence_duration = 0
        self.max_silence_duration = self.config.max_silence_duration
        self.min_audio_duration = self.config.min_audio_duration
        self.sample_rate = self.config.audio_rate
        self.conversation_history = []

        # Initialize questionnaire handler
        self.questionnaire_handler = Question<PERSON><PERSON><PERSON><PERSON>()
        self.chat_started = False

        # Initialize AI agent
        self.inputs = {}

    def _detect_application_and_form(self, user_input):
        """Initialize the AI agent for processing voice inputs"""
        try:
            # Load forms and RAG model
            with open(self.config.forms_path, "r") as file:
                applications_json = json.load(file)

            app_chain = RAGChain(file_path=self.config.file_path, index_path=self.config.index_path, model_name=self.config.model_name)
            if index_exists(self.config.index_path):
                app_chain.create_or_load_vectorstore()
            else:
                documents = app_chain.load_documents()
                app_chain.create_or_load_vectorstore(documents)

            app_chain.build_chain()
            app_chain_raw = app_chain(user_input=user_input, applications=json.dumps(applications_json, indent=2))
            app_answer = app_chain_raw['answer']

            try:
                cleaned = re.sub(r"^```(?:json)?\s*", "", app_answer.strip())
                cleaned = re.sub(r"\s*```$", "", cleaned)
                parsed = json.loads(cleaned)
                # application_name = parsed["application"]
                # form_name = parsed["form"]
                application_name = "Accounting"
                form_name = "PRINT_CASH_RECEIPTS"
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON: {e}")

            # Use default application and form for streaming mode
            application_name = self.config.default_application
            form_name = self.config.default_form

            application_forms = applications_json[application_name]
            application_hotkey = application_forms["application_hotkey"]
            form_hotkey = applications_json[application_name]['forms_hotkey'][form_name]

            self.inputs = {
                "input": "",
                "conversation_history": "",
                "application_hotkey": application_hotkey,
                "form_hotkey": form_hotkey
            }
            logger.info("AI agent initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing AI agent: {e}")

    def process_audio_chunk(self, audio_data):
        """Process incoming audio chunk and detect speech"""
        try:
            # Convert base64 to numpy array
            audio_bytes = base64.b64decode(audio_data)

            # Handle different audio formats from browser
            if len(audio_bytes) % 4 == 0:
                # Try float32 first
                try:
                    audio_array = np.frombuffer(audio_bytes, dtype=np.float32)
                except:
                    # Fallback to int16 and convert
                    audio_array = np.frombuffer(audio_bytes, dtype=np.int16).astype(np.float32) / 32768.0
            else:
                # Likely int16 data
                audio_array = np.frombuffer(audio_bytes, dtype=np.int16).astype(np.float32) / 32768.0

            # Ensure audio is in valid range [-1, 1]
            audio_array = np.clip(audio_array, -1.0, 1.0)

            # Skip empty or invalid audio
            if len(audio_array) == 0:
                return {"status": "silence", "rms": 0.0}

            # Calculate RMS to detect speech (with safety checks)
            audio_squared = audio_array ** 2
            if np.any(np.isnan(audio_squared)) or np.any(np.isinf(audio_squared)):
                return {"status": "silence", "rms": 0.0}

            rms = np.sqrt(np.mean(audio_squared))
            if np.isnan(rms) or np.isinf(rms):
                rms = 0.0

            if rms > self.silence_threshold:
                # Speech detected
                self.is_recording = True
                self.silence_duration = 0
                self.audio_buffer.extend(audio_array)
                return {"status": "recording", "rms": float(rms)}
            else:
                # Silence detected
                if self.is_recording:
                    self.silence_duration += len(audio_array) / self.sample_rate
                    self.audio_buffer.extend(audio_array)

                    # Check if we should stop recording
                    if self.silence_duration >= self.max_silence_duration:
                        return self.finalize_recording()

                return {"status": "silence", "rms": float(rms)}

        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")
            return {"status": "error", "message": str(e)}
    
    def finalize_recording(self):
        """Finalize the current recording and return audio data"""
        try:
            if len(self.audio_buffer) == 0:
                self.reset_audio_only()
                return {"status": "no_audio"}
                
            # Check minimum duration
            duration = len(self.audio_buffer) / self.sample_rate
            if duration < self.min_audio_duration:
                self.reset_audio_only()
                return {"status": "too_short", "duration": duration}
                
            # Convert to bytes
            audio_array = np.array(self.audio_buffer, dtype=np.float32)

            # Ensure audio is in valid range
            audio_array = np.clip(audio_array, -1.0, 1.0)

            # Remove any NaN or inf values
            audio_array = audio_array[np.isfinite(audio_array)]

            if len(audio_array) == 0:
                self.reset_audio_only()
                return {"status": "no_audio"}

            # Normalize audio safely
            max_val = np.max(np.abs(audio_array))
            if max_val > 0 and np.isfinite(max_val):
                audio_array = audio_array / max_val

            # Convert to 16-bit PCM safely
            audio_int16 = np.clip(audio_array * 32767, -32768, 32767).astype(np.int16)
            
            # Create WAV file in memory
            wav_buffer = BytesIO()
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(self.sample_rate)
                wav_file.writeframes(audio_int16.tobytes())
            
            wav_data = wav_buffer.getvalue()
            self.reset_audio_only()
            
            # Save to temporary file for transcription
            with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tf:
                tf.write(wav_data)
                wav_path = tf.name
            
            # Transcribe audio
            transcription = get_transcribe_audio(
                wav_path,
                model=self.config.transcription_model,
                language=self.config.transcription_language,
                prompt=self.config.transcription_prompt,
                temperature=self.config.transcription_temperature
            )
            
            # Process with questionnaire handler first
            ai_response = None
            ai_audio_base64 = None

            if transcription.strip():
                try:
                    # Check if this is the first user message
                    if not self.chat_started:
                        self.chat_started = True
                        # Send first question
                        ai_response = self.questionnaire_handler.get_first_question()

                        self._detect_application_and_form(transcription)

                        # Add to conversation history
                        self.conversation_history.append(f"User: {transcription}")
                        self.conversation_history.append(f"Assistant: {ai_response}")

                    else:
                        # Process through questionnaire handler for subsequent messages
                        response, needs_llm, context = self.questionnaire_handler.process_user_input(transcription)

                        if needs_llm:
                            # Separate agent handling based on mode
                            mode = context.get("mode", "intervention")
                            agent_type = context.get("agent_type", "intervention")
                            logger.info(f"Agent needed: {agent_type} for {mode}")

                            # Add user message to conversation
                            self.conversation_history.append(f"User: {transcription}")

                            # Update inputs for agent
                            inputs = self.inputs.copy()
                            inputs["conversation_history"] = "\n".join(self.conversation_history)

                            if agent_type == "intervention":
                                # Use intervention agent for user deviations
                                from utils import intervention_agent

                                # Update intervention context with current user input
                                intervention_context = context.get("intervention_context", {})
                                intervention_context["user_input"] = transcription
                                intervention_context["conversation_history"] = "\n".join(self.conversation_history)
                                context["intervention_context"] = intervention_context

                                llm_response = intervention_agent(inputs, context)

                                # Check if intervention agent extracted an answer
                                final_response, continue_with_llm, response_context = self.questionnaire_handler.handle_llm_response(llm_response)

                                if continue_with_llm:
                                    ai_response = llm_response
                                    logger.info("Intervention agent continues handling conversation")
                                else:
                                    ai_response = final_response
                                    logger.info("Questionnaire resumed after intervention")

                            elif agent_type == "final_validation":
                                # Use final validation agent for form completion
                                from utils import final_validation_agent

                                # Prepare validation context with conversation history
                                validation_context = context.get("validation_context", {})
                                validation_context["conversation_history"] = "\n".join(self.conversation_history)
                                context["validation_context"] = validation_context

                                llm_response = final_validation_agent(inputs, context)

                                # Check if validation agent completed the form
                                final_response, continue_with_llm, response_context = self.questionnaire_handler.handle_llm_response(llm_response)

                                if response_context.get("mode") == "form_completed":
                                    ai_response = final_response
                                    logger.info("Form completed successfully by validation agent")
                                elif continue_with_llm:
                                    ai_response = llm_response
                                    logger.info("Validation agent continues collecting missing information")
                                else:
                                    ai_response = final_response
                                    logger.info("Validation completed")
                            else:
                                logger.error(f"Unknown agent type: {agent_type}")

                            # Add AI response to conversation
                            self.conversation_history.append(f"Assistant: {ai_response}")

                        else:
                            # Questionnaire mode - no LLM needed
                            ai_response = response
                            logger.info(f"Questionnaire response: {ai_response[:100]}...")

                            # Add to conversation history
                            self.conversation_history.append(f"User: {transcription}")
                            self.conversation_history.append(f"Assistant: {ai_response}")

                    # Generate TTS audio for AI response
                    if self.config.enable_tts and ai_response and ai_response.strip():
                        try:
                            logger.info("Generating TTS audio for response...")
                            tts_audio = create_tts_response(ai_response)
                            ai_audio_base64 = base64.b64encode(tts_audio).decode()
                            logger.info("TTS audio generated successfully")
                        except Exception as e:
                            logger.error(f"Error generating TTS: {e}")

                except Exception as e:
                    logger.error(f"Error processing response: {e}")
                    ai_response = "Sorry, I encountered an error processing your request."

            return {
                "status": "transcribed",
                "text": transcription,
                "ai_response": ai_response,
                "ai_audio": ai_audio_base64,
                "duration": duration,
                "audio_file": wav_path
            }
            
        except Exception as e:
            logger.error(f"Error finalizing recording: {e}")
            self.reset_audio_only()
            return {"status": "error", "message": str(e)}
    
    def reset(self):
        """Reset the audio buffer and chat state"""
        self.audio_buffer = []
        self.is_recording = False
        self.silence_duration = 0
        self.chat_started = False

    def reset_audio_only(self):
        """Reset only the audio buffer, keep chat state"""
        self.audio_buffer = []
        self.is_recording = False
        self.silence_duration = 0

async def handle_voice_stream(websocket):
    """Handle WebSocket connections for voice streaming"""
    logger.info(f"New WebSocket connection: {websocket.remote_address}")
    processor = VoiceStreamProcessor()
    
    try:
        async for message in websocket:
            try:
                # Handle both string and bytes messages
                if isinstance(message, bytes):
                    message = message.decode('utf-8')

                data = json.loads(message)

                if data.get("type") == "audio_chunk":
                    # Process audio chunk
                    audio_data = data.get("audio_data")
                    if audio_data:
                        result = processor.process_audio_chunk(audio_data)
                        await websocket.send(json.dumps(result))

                elif data.get("type") == "start_recording":
                    # Start recording session (reset audio buffer only)
                    processor.reset_audio_only()
                    await websocket.send(json.dumps({"status": "recording_started"}))

                elif data.get("type") == "stop_recording":
                    # Stop recording and finalize
                    result = processor.finalize_recording()
                    await websocket.send(json.dumps(result))

                elif data.get("type") == "ping":
                    # Health check
                    await websocket.send(json.dumps({"status": "pong"}))

                else:
                    await websocket.send(json.dumps({"status": "unknown_command"}))

            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                try:
                    await websocket.send(json.dumps({"status": "invalid_json"}))
                except:
                    pass  # Connection might be closed
            except Exception as e:
                logger.error(f"Error handling message: {e}")
                try:
                    await websocket.send(json.dumps({"status": "error", "message": str(e)}))
                except:
                    pass  # Connection might be closed

    except websockets.exceptions.ConnectionClosed:
        logger.info(f"WebSocket connection closed: {websocket.remote_address}")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")

def start_voice_server(host="localhost", port=8765):
    """Start the WebSocket voice server"""
    logger.info(f"Starting voice WebSocket server on {host}:{port}")

    async def main():
        async with websockets.serve(handle_voice_stream, host, port):
            logger.info("Voice server started successfully")
            await asyncio.Future()  # run forever

    asyncio.run(main())

if __name__ == "__main__":
    start_voice_server()
