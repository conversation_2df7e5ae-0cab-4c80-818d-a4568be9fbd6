import json
from typing import Callable

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import PromptTemplate
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import PyPDFLoader
from langchain_litellm import Chat<PERSON><PERSON>LL<PERSON>
from langchain.chains.retrieval import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_openai import OpenAIEmbeddings
from langchain.chains.llm import LLMChain

from prompts.apps import APP_FORM_PROMPT
from prompts.accounting import FORMAT_CASH_RECEIPT_TOOL, VALIDATE_CASH_RECEIPT_TOOL, LLM_INTERVENTION_AGENT, FINAL_VALIDATION_AGENT




class RAGChain:
    """Chain to extract context related to user request and format a response to CUANavChain."""

    def __init__(self, file_path: str, index_path: str = None, model_name: str = "gpt-4o"):
        self.file_path = file_path
        self.index_path = index_path
        self.model_name = model_name
        self.embedding_model = OpenAIEmbeddings()
        self.vectorstore = None
        self.rag_chain = None

    def load_documents(self, chunk_size=500, chunk_overlap=50):
        loader = PyPDFLoader(self.file_path)
        docs = loader.load()
        splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
        return splitter.split_documents(docs)

    def create_or_load_vectorstore(self, documents=[]):
        if self.index_path:
            try:
                self.vectorstore = FAISS.load_local(self.index_path, self.embedding_model, allow_dangerous_deserialization=True)
                print(f"Loaded existing index from: {self.index_path}")
            except:
                print("No existing index found. Creating a new one...")
                self.vectorstore = FAISS.from_documents(documents, self.embedding_model)
                self.vectorstore.save_local(self.index_path)
        else:
            self.vectorstore = FAISS.from_documents(documents, self.embedding_model)

    def build_chain(self):
        retriever = self.vectorstore.as_retriever(search_type="similarity", k=3)
        llm = ChatLiteLLM(model_name=self.model_name)

        # Define prompt template with both variables
        prompt = PromptTemplate(
            input_variables=["input", "context", "applications"],
            template=APP_FORM_PROMPT
        )

        question_answer_chain  = create_stuff_documents_chain(llm, prompt)
        self.rag_chain = create_retrieval_chain(retriever, question_answer_chain)

    def invoke(self, user_input: str, applications: str):
        if not self.rag_chain:
            raise ValueError("RAG chain is not initialized. Call build_chain() first.")
        return self.rag_chain.invoke({"input": user_input, "applications": applications})

    def __call__(self, user_input: str, applications: str):
        return self.invoke(user_input, applications)

    def interactive_chat(self, user_input: str, applications: str):
        print("RAG Agent is ready. Type 'exit' to quit.")
        while True:
            query = input("\n> ")
            if query.lower() in ("exit", "quit"):
                break
            try:
                print("🧠", self.invoke({"input": user_input, "applications": applications}))
            except Exception as e:
                print(f"Error: {e}")

class ValidationChain(LLMChain):
    """Chain to validate the completeness and correctness of collected information."""

    @classmethod
    def from_llm(cls, llm: ChatLiteLLM, verbose: bool = True) -> LLMChain:
        """Get the validation chain."""
        prompt = PromptTemplate(
            template=VALIDATE_CASH_RECEIPT_TOOL,
            input_variables=["input", "conversation_history", "current_date"],
        )
        return cls(prompt=prompt, llm=llm, verbose=verbose)


class InterventionChain(LLMChain):
    """Chain to handle user deviations during questionnaire flow."""

    @classmethod
    def from_llm(cls, llm: ChatLiteLLM, verbose: bool = True) -> LLMChain:
        """Get the intervention chain."""
        prompt = PromptTemplate(
            template=LLM_INTERVENTION_AGENT,
            input_variables=["current_question", "expected_type", "user_input", "progress", "current_date"],
        )
        return cls(prompt=prompt, llm=llm, verbose=verbose)


class FinalValidationChain(LLMChain):
    """Chain to perform final validation and form submission."""

    @classmethod
    def from_llm(cls, llm: ChatLiteLLM, verbose: bool = True) -> LLMChain:
        """Get the final validation chain."""
        prompt = PromptTemplate(
            template=FINAL_VALIDATION_AGENT,
            input_variables=["collected_answers", "conversation_history", "current_date"],
        )
        return cls(prompt=prompt, llm=llm, verbose=verbose)


class JSONFormattingChain(LLMChain):
    """Chain to format the information collected from user into JSON."""

    @classmethod
    def from_llm(cls, llm: ChatLiteLLM, verbose: bool = True) -> LLMChain:
        """Get the response parser."""
        prompt = PromptTemplate(
            template=FORMAT_CASH_RECEIPT_TOOL,
            input_variables=["input", "conversation_history"],
        )
        return cls(prompt=prompt, llm=llm, verbose=verbose)