"""
Questionnaire Handler for Streaming Voice Chat

This module handles the hybrid questionnaire flow:
1. Pre-defined questions without LLM
2. LLM intervention when user deviates
3. Seamless handover between modes
"""

import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum

# Word to number mapping for speech-to-text robustness
WORD_TO_NUMBER = {
    'zero': 0, 'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
    'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10,
    'eleven': 11, 'twelve': 12, 'thirteen': 13, 'fourteen': 14, 'fifteen': 15,
    'sixteen': 16, 'seventeen': 17, 'eighteen': 18, 'nineteen': 19, 'twenty': 20,
    'twenty-one': 21, 'twenty-two': 22, 'twenty-three': 23, 'twenty-four': 24,
    'twenty-five': 25, 'twenty-six': 26, 'twenty-seven': 27, 'twenty-eight': 28,
    'twenty-nine': 29, 'thirty': 30, 'thirty-one': 31, 'thirty-two': 32,
    'first': 1, 'second': 2, 'third': 3, 'fourth': 4, 'fifth': 5,
    'sixth': 6, 'seventh': 7, 'eighth': 8, 'ninth': 9, 'tenth': 10,
    'eleventh': 11, 'twelfth': 12, 'thirteenth': 13, 'fourteenth': 14, 'fifteenth': 15,
    'sixteenth': 16, 'seventeenth': 17, 'eighteenth': 18, 'nineteenth': 19, 'twentieth': 20,
    'twenty-first': 21, 'twenty-second': 22, 'twenty-third': 23, 'twenty-fourth': 24,
    'twenty-fifth': 25, 'twenty-sixth': 26, 'twenty-seventh': 27, 'twenty-eighth': 28,
    'twenty-ninth': 29, 'thirtieth': 30, 'thirty-first': 31,
    'forty': 40, 'fifty': 50, 'sixty': 60, 'seventy': 70, 'eighty': 80, 'ninety': 90,
    'hundred': 100, 'thousand': 1000
}

logger = logging.getLogger(__name__)

def convert_words_to_numbers(text: str) -> str:
    """Convert spoken number words to digits in text"""
    text_lower = text.lower().strip()
    words = text_lower.split()

    # Handle thousands like "three thousand" first (before single word conversion)
    if 'thousand' in text_lower:
        thousand_match = re.search(r'(\w+)\s+thousand', text_lower)
        if thousand_match:
            word = thousand_match.group(1)
            if word in WORD_TO_NUMBER:
                return text_lower.replace(f"{word} thousand", str(WORD_TO_NUMBER[word] * 1000))

    # Handle hundreds with additional numbers like "one hundred fifty"
    if 'hundred' in text_lower:
        # Pattern: "one hundred fifty" → 150
        hundred_match = re.search(r'(\w+)\s+hundred(?:\s+(\w+))?', text_lower)
        if hundred_match:
            base_word = hundred_match.group(1)
            extra_word = hundred_match.group(2)

            if base_word in WORD_TO_NUMBER:
                base_value = WORD_TO_NUMBER[base_word] * 100
                if extra_word and extra_word in WORD_TO_NUMBER:
                    return str(base_value + WORD_TO_NUMBER[extra_word])
                else:
                    return str(base_value)

    # Look for compound numbers like "twenty-three" or "twenty three" in the sentence
    for i, word in enumerate(words):
        # Remove punctuation from word for matching
        clean_word = re.sub(r'[^\w-]', '', word)

        # Check for hyphenated compound numbers
        if '-' in clean_word and clean_word in WORD_TO_NUMBER:
            # Replace the word with its numeric equivalent in the original text
            return text_lower.replace(word, str(WORD_TO_NUMBER[clean_word]))

        # Check for space-separated compound numbers like "twenty three"
        if i < len(words) - 1:
            clean_next_word = re.sub(r'[^\w-]', '', words[i+1])
            compound_with_space = f"{clean_word} {clean_next_word}"
            compound_with_hyphen = f"{clean_word}-{clean_next_word}"
            if compound_with_hyphen in WORD_TO_NUMBER:
                # Replace both words with the numeric equivalent
                original_compound = f"{word} {words[i+1]}"
                return text_lower.replace(original_compound, str(WORD_TO_NUMBER[compound_with_hyphen]))

        # Check for single number words
        if clean_word in WORD_TO_NUMBER:
            # Replace the word with its numeric equivalent
            return text_lower.replace(word, str(WORD_TO_NUMBER[clean_word]))

    # Handle multi-word numbers like "seven eight nine" → "789"
    if len(words) > 1:
        # Check if all words are single digit numbers
        digits = []
        for word in words:
            if word in WORD_TO_NUMBER and WORD_TO_NUMBER[word] < 10:
                digits.append(str(WORD_TO_NUMBER[word]))
            else:
                break

        if len(digits) == len(words):
            return ''.join(digits)



    # Check if the entire text is a number word
    if text_lower in WORD_TO_NUMBER:
        return str(WORD_TO_NUMBER[text_lower])

    # Check for compound numbers like "twenty one" (with space)
    if len(words) == 2:
        compound = '-'.join(words)
        if compound in WORD_TO_NUMBER:
            return str(WORD_TO_NUMBER[compound])

        # Handle cases like "twenty one" -> "twenty-one"
        if words[0] in ['twenty', 'thirty'] and words[1] in WORD_TO_NUMBER:
            if words[0] == 'twenty':
                return str(20 + WORD_TO_NUMBER[words[1]])
            elif words[0] == 'thirty':
                return str(30 + WORD_TO_NUMBER[words[1]])

    # If no conversion found, return original text
    return text

class ChatMode(Enum):
    QUESTIONNAIRE = "questionnaire"
    LLM_INTERVENTION = "llm_intervention"

class QuestionType(Enum):
    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    CHOICE = "choice"
    YES_NO = "yes_no"
    BOOLEAN = "boolean"

class QuestionnaireHandler:
    def __init__(self):
        self.current_question_index = 0
        self.chat_mode = ChatMode.QUESTIONNAIRE
        self.collected_answers = {}
        self.questions = self._load_questionnaire()
        self.llm_context = ""
        self.deviation_count = 0
        
    def _load_questionnaire(self) -> List[Dict]:
        """Load the predefined questionnaire from config file"""
        try:
            with open("questionnaire_config.json", "r") as f:
                config = json.load(f)

            questions = []
            for q in config["questionnaire"]:
                # Convert string type to enum
                q["type"] = QuestionType(q["type"])
                questions.append(q)

            return questions
        except Exception as e:
            logger.error(f"Error loading questionnaire config: {e}")
            # Fallback to default questionnaire
            return []
    
    def process_user_input(self, user_input: str) -> Tuple[str, bool, Dict]:
        """
        Process user input and return response, LLM_needed flag, and context
        
        Returns:
            Tuple[response, needs_llm, context]
        """
        user_input = user_input.lower().strip()
        
        if self.chat_mode == ChatMode.QUESTIONNAIRE:
            return self._handle_questionnaire_mode(user_input)
        else:
            # This will be handled by LLM (either intervention or final validation)
            return self._handle_llm_mode(user_input)
    
    def _handle_questionnaire_mode(self, user_input: str) -> Tuple[str, bool, Dict]:
        """Handle input when in questionnaire mode"""
        
        if self.current_question_index >= len(self.questions):
            return self._complete_questionnaire()
        
        current_question = self.questions[self.current_question_index]
        
        # Try to extract answer from user input
        extracted_answer = self._extract_answer(user_input, current_question)

        if extracted_answer is not None:
            # Valid answer found
            self.collected_answers[current_question["id"]] = extracted_answer
            self.current_question_index += 1
            
            # Move to next question or complete
            if self.current_question_index >= len(self.questions):
                return self._complete_questionnaire()
            else:
                next_question = self._format_next_question()
                return next_question, False, {}
        else:
            # No valid answer found - hand over to LLM
            print("***********************", "LLM")
            return self._initiate_llm_intervention(user_input, current_question)



    def _handle_llm_mode(self, user_input: str) -> Tuple[str, bool, Dict]:
        """Handle input when LLM should take over"""
        # This method prepares context for LLM intervention or final validation
        current_question = self.questions[self.current_question_index] if self.current_question_index < len(self.questions) else None

        # Determine if this is intervention during questionnaire or final validation
        if current_question is None:
            # Final validation mode
            context = {
                "mode": "final_validation",
                "agent_type": "final_validation",
                "user_input": user_input,
                "current_question": None,
                "collected_answers": self.collected_answers,
                "questionnaire_progress": f"{len(self.questions)}/{len(self.questions)}",
                "deviation_count": self.deviation_count,
                "validation_context": self._get_final_validation_context()
            }
        else:
            # Intervention mode
            context = {
                "mode": "intervention",
                "agent_type": "intervention",
                "user_input": user_input,
                "current_question": current_question,
                "collected_answers": self.collected_answers,
                "questionnaire_progress": f"{self.current_question_index + 1}/{len(self.questions)}",
                "deviation_count": self.deviation_count,
                "intervention_context": self._get_intervention_context(user_input, current_question)
            }

        return "", True, context
    
    def _extract_answer(self, user_input: str, question: Dict) -> Optional[Any]:
        """Extract and validate answer from user input"""

        # Special handling for reference number
        if question["id"] == "reference_number":
            result = self._extract_reference_number(user_input)
            # Validate that reference number is alphanumeric and reasonable length
            if result and len(result) >= 3 and re.match(r'^[A-Za-z0-9]+$', result):
                # Additional check: make sure it's not just extracted from common words
                common_words = ["don", "have", "one", "usual", "default", "know", "sure", "think", "remember"]
                if result.lower() not in common_words:
                    return result
            return None

        # First, try to convert spoken numbers to digits
        converted_input = convert_words_to_numbers(user_input)

        # For number fields, prioritize converted input to ensure word numbers are properly converted
        if question["type"] == "number":
            inputs_to_try = [converted_input, user_input] if converted_input != user_input else [user_input]
        else:
            inputs_to_try = [user_input, converted_input] if converted_input != user_input else [user_input]

        for input_text in inputs_to_try:
            # Skip catch-all patterns like (.*) - only use specific patterns
            specific_patterns = [p for p in question.get("validation_patterns", []) if p != "(.*)"]

            for pattern in specific_patterns:
                match = re.search(pattern, input_text, re.IGNORECASE)
                if match:
                    try:
                        # Handle special validation for amount_in_100_bills first
                        if question["id"] == "amount_in_100_bills":
                            result = self._validate_100_bills_amount(match.group(1))
                            if result is not None:
                                return result
                            # If validation failed, continue to try other patterns
                        elif question["type"] == QuestionType.CHOICE:
                            result = self._normalize_choice_answer(match.group(1), question)
                            if result and result in question.get("choices", []):
                                return result
                        elif question["type"] == QuestionType.NUMBER:
                            result = self._normalize_number_answer(match.group(1))
                            if result and self._validate_number_field(result, question["id"]):
                                # Additional check: make sure the number isn't extracted from non-numeric context
                                # Use original user_input for context validation, not the converted input
                                context_valid = self._is_valid_number_context(user_input, result)
                                # Debug output for troubleshooting
                                if user_input == "the main one":
                                    print(f"DEBUG: pattern='{pattern}', match='{match.group(1)}', result='{result}', context_valid={context_valid}")
                                if context_valid:
                                    return result
                                # If context validation fails, continue to next pattern
                                continue
                        elif question["type"] == QuestionType.DATE:
                            result = self._normalize_date_answer(match.group(1), question["id"])
                            if result:
                                return result
                        elif question["type"] == QuestionType.YES_NO:
                            return self._normalize_yes_no_answer(match.group(1))
                        elif question["type"] == QuestionType.BOOLEAN:
                            return self._normalize_boolean_answer(match.group(1))
                        else:
                            return match.group(1)
                    except (ValueError, TypeError):
                        # If normalization fails, continue to next pattern
                        continue

        return None

    def _validate_number_field(self, value: str, field_id: str) -> bool:
        """Validate that a number field contains a valid value for its type"""
        try:
            if field_id in ["bank_code", "customer_number", "employee_number", "account_number"]:
                # These should be integers
                int_val = int(float(value))
                return int_val > 0
            elif field_id == "amount":
                # This should be a positive float
                float_val = float(value)
                return float_val > 0
            elif field_id == "amount_in_100_bills":
                # This should be 0 or multiple of 100
                int_val = int(float(value))
                return int_val >= 0 and int_val % 100 == 0
            else:
                # Default: just check if it's a valid number
                float(value)
                return True
        except (ValueError, TypeError):
            return False

    def _is_valid_number_context(self, user_input: str, extracted_number: str) -> bool:
        """Check if the extracted number is in a valid context (not part of a negative phrase)"""
        user_input_lower = user_input.lower()

        # Check for negative contexts that indicate the user doesn't have a number
        negative_phrases = [
            "don't know", "not sure", "don't have", "no idea", "not certain",
            "the main", "the usual", "the default", "the regular", "the normal",
            "i think", "maybe", "probably", "might be", "could be"
        ]

        for phrase in negative_phrases:
            if phrase in user_input_lower:
                return False

        # If the extracted number is just "one" and it's in a phrase like "the main one", reject it
        if extracted_number.lower() in ["1", "one"] and any(word in user_input_lower for word in ["main", "usual", "default", "regular", "normal"]):
            return False

        return True

    def _extract_reference_number(self, user_input: str) -> Optional[str]:
        """Extract reference number with intelligent pattern matching"""

        def convert_spoken_reference(text):
            """Convert spoken reference numbers like 'zero six two three two zero two five C A' to '06232025CA'"""

            # First, handle spoken digits
            digit_words = {
                'zero': '0', 'one': '1', 'two': '2', 'three': '3', 'four': '4',
                'five': '5', 'six': '6', 'seven': '7', 'eight': '8', 'nine': '9'
            }

            words = text.lower().split()
            result_parts = []
            current_sequence = []

            for word in words:
                if word in digit_words:
                    current_sequence.append(digit_words[word])
                elif len(word) == 1 and word.isalpha():
                    # Single letter
                    if current_sequence:
                        result_parts.append(''.join(current_sequence))
                        current_sequence = []
                    current_sequence.append(word.upper())
                else:
                    # Regular word
                    if current_sequence:
                        result_parts.append(''.join(current_sequence))
                        current_sequence = []
                    result_parts.append(word)

            if current_sequence:
                result_parts.append(''.join(current_sequence))

            return ' '.join(result_parts)

        # Convert spoken reference and regular number conversion
        spoken_converted = convert_spoken_reference(user_input)
        regular_converted = convert_words_to_numbers(user_input)

        # Try multiple strategies to extract reference number
        strategies = [
            # Strategy 1: Look for patterns after keywords like "is", "number", etc.
            r"(?:is|number|ref|reference|code)\s+([A-Za-z0-9]{3,})",

            # Strategy 2: Look for date-like patterns with letters (common format: MMDDYYYYXX)
            r"\b([0-9]{6,8}[A-Za-z]{1,3})\b",

            # Strategy 3: Look for any alphanumeric string 6+ characters
            r"\b([A-Za-z0-9]{6,})\b",

            # Strategy 4: Look for mixed alphanumeric patterns
            r"\b([0-9]+[A-Za-z]+|[A-Za-z]+[0-9]+)\b",

            # Strategy 5: Look for shorter alphanumeric patterns (3+ chars)
            r"\b([A-Za-z0-9]{3,})\b"
        ]

        # Try different input variations
        inputs_to_try = [spoken_converted, regular_converted, user_input]

        for input_text in inputs_to_try:
            # First, try to find reference number patterns anywhere in the text
            ref_patterns = [
                r'\b([0-9]{6,8}[A-Za-z]{1,3})\b',  # Date-like with letters (MMDDYYYYXX)
                r'\b([A-Za-z0-9]{6,})\b',          # Any alphanumeric 6+ chars
                r'\b([0-9]+[A-Za-z]+)\b',          # Numbers followed by letters
                r'\b([A-Za-z]+[0-9]+)\b'           # Letters followed by numbers
            ]

            for pattern in ref_patterns:
                pattern_match = re.search(pattern, input_text)
                if pattern_match:
                    candidate = pattern_match.group(1)
                    # Make sure it's not a common word
                    excluded_words = {'number', 'is', 'the', 'ref', 'reference', 'code', 'deposit', 'bank', 'as', 'i', 'remember', 'said', 'think', 'for', 'sure', 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'}
                    if candidate.lower() not in excluded_words and len(candidate) >= 3:
                        return candidate.upper()

            # Fallback: Look for reference number after keywords
            ref_match = re.search(r"(?:is|number|ref|reference|code)\s+(.+)", input_text, re.IGNORECASE)
            if ref_match:
                ref_part = ref_match.group(1).strip()

                # First, try to find a complete reference number pattern (like 06232025CA)
                # Look for patterns that are likely reference numbers: digits followed by letters
                ref_patterns = [
                    r'\b([0-9]{6,8}[A-Za-z]{1,3})\b',  # Date-like with letters (MMDDYYYYXX)
                    r'\b([A-Za-z0-9]{6,})\b',          # Any alphanumeric 6+ chars
                    r'\b([0-9]+[A-Za-z]+)\b',          # Numbers followed by letters
                    r'\b([A-Za-z]+[0-9]+)\b'           # Letters followed by numbers
                ]

                for i, pattern in enumerate(ref_patterns):
                    pattern_match = re.search(pattern, ref_part)
                    if pattern_match:
                        candidate = pattern_match.group(1)
                        print(f"DEBUG: Pattern {i+1} matched: '{candidate}'")
                        # Make sure it's not a common word
                        excluded_words = {'number', 'is', 'the', 'ref', 'reference', 'code', 'deposit', 'bank', 'as', 'i', 'remember', 'said', 'think', 'for', 'sure', 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'}
                        if candidate.lower() not in excluded_words and len(candidate) >= 3:
                            print(f"DEBUG: Returning candidate: '{candidate.upper()}'")
                            return candidate.upper()
                        else:
                            print(f"DEBUG: Candidate rejected (excluded word or too short)")
                    else:
                        print(f"DEBUG: Pattern {i+1} no match")

                # Fallback: Extract alphanumeric sequences but stop at the first valid one
                alphanumeric_parts = re.findall(r'[A-Za-z0-9]+', ref_part)
                if alphanumeric_parts:
                    excluded_words = {'number', 'is', 'the', 'ref', 'reference', 'code', 'deposit', 'bank', 'as', 'i', 'remember', 'said', 'think', 'for', 'sure', 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'}

                    # Look for the first part that looks like a reference number
                    for part in alphanumeric_parts:
                        if part.lower() not in excluded_words and len(part) >= 6:
                            return part.upper()

                    # If no long parts found, try shorter ones
                    for part in alphanumeric_parts:
                        if part.lower() not in excluded_words and len(part) >= 3:
                            return part.upper()

            # Regular pattern matching as fallback
            for pattern in strategies:
                matches = re.findall(pattern, input_text, re.IGNORECASE)
                if matches:
                    # Filter out common words that aren't reference numbers
                    excluded_words = {'number', 'is', 'the', 'ref', 'reference', 'code', 'deposit', 'bank', 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'}

                    for match in matches:
                        if match.lower() not in excluded_words and len(match) >= 3:
                            # Clean the reference number (remove spaces, convert to uppercase)
                            cleaned = re.sub(r'[^A-Za-z0-9]', '', match).upper()
                            if len(cleaned) >= 3:
                                return cleaned

        return None

    def _normalize_choice_answer(self, answer: str, question: Dict) -> Optional[str]:
        """Normalize choice answers"""
        answer = answer.lower()

        # Handle pay code choices
        if "business" in answer and "check" in answer:
            return "BUSINESS CHECK"
        elif "bank" in answer and "draft" in answer:
            return "BANK DRAFT"
        elif answer == "cash" or "cash" in answer:
            return "CASH"
        elif "cashier" in answer and "check" in answer:
            return "CASHIER'S CHECK"
        elif "credit" in answer and "card" in answer:
            return "CREDIT CARD"
        elif "electronic" in answer and "transfer" in answer:
            return "ELECTRONIC TRANSFER"
        elif "load" in answer and "proceeds" in answer and "check" in answer:
            return "LOAD PROCEEDS CHECK"
        elif "money" in answer and "order" in answer:
            return "MONEY ORDER"
        elif "personal" in answer and "check" in answer:
            return "PERSONAL CHECK"
        elif "traveler" in answer and "check" in answer:
            return "TRAVELER'S CHECK"

        # Check if the answer matches any of the valid choices
        valid_choices = question.get("choices", [])
        answer_upper = answer.upper()

        for choice in valid_choices:
            if choice.upper() == answer_upper:
                return choice

        # No valid choice found - return None to trigger LLM intervention
        return None
    
    def _normalize_number_answer(self, answer: str) -> Optional[str]:
        """Normalize number answers"""
        # Handle special spoken number cases first (before general conversion)
        answer_lower = answer.lower().strip()

        # Handle "I got" or similar prefixes
        if "got" in answer_lower or "received" in answer_lower:
            # Extract the amount part after these words
            amount_match = re.search(r"(?:got|received)\s+(.+)", answer_lower)
            if amount_match:
                amount_part = amount_match.group(1).strip()
                # Recursively process the extracted amount
                return self._normalize_number_answer(amount_part)

        # Handle comma-separated numbers (e.g., "3,000" or "$3,000")
        comma_number_match = re.search(r'\$?([0-9,]+(?:\.[0-9]{1,2})?)', answer)
        if comma_number_match:
            number_str = comma_number_match.group(1)
            # Remove commas and validate it's a proper number
            clean_number = number_str.replace(',', '')
            try:
                # Validate it's a proper number
                float(clean_number)
                return clean_number
            except ValueError:
                pass

        # Handle thousands with additional numbers (e.g., "one thousand five hundred")
        if "thousand" in answer_lower:
            # Pattern: "one thousand five hundred" or "two thousand"
            thousand_match = re.search(r'(\w+)\s+thousand(?:\s+(\w+)(?:\s+(\w+))?)?', answer_lower)
            if thousand_match:
                base_word = thousand_match.group(1)
                extra_word1 = thousand_match.group(2)
                extra_word2 = thousand_match.group(3)

                # Convert base word to number and multiply by 1000
                base_converted = convert_words_to_numbers(base_word)
                try:
                    base_number = float(base_converted) * 1000

                    # Add hundreds if present (e.g., "five hundred")
                    if extra_word1 and extra_word2:
                        if extra_word2 == "hundred":
                            extra_converted = convert_words_to_numbers(extra_word1)
                            try:
                                extra_number = float(extra_converted) * 100
                                result = int(base_number + extra_number)
                                return str(result)
                            except ValueError:
                                pass
                        else:
                            # Try to combine the extra words
                            extra_phrase = f"{extra_word1} {extra_word2}"
                            extra_converted = convert_words_to_numbers(extra_phrase)
                            try:
                                extra_number = float(extra_converted)
                                result = int(base_number + extra_number)
                                return str(result)
                            except ValueError:
                                pass
                    elif extra_word1:
                        # Single extra word
                        extra_converted = convert_words_to_numbers(extra_word1)
                        try:
                            extra_number = float(extra_converted)
                            result = int(base_number + extra_number)
                            return str(result)
                        except ValueError:
                            pass

                    return str(int(base_number))
                except ValueError:
                    pass

        # Handle hundreds
        if "hundred" in answer_lower:
            # Handle patterns like "three hundred fifty"
            hundred_match = re.search(r'(\w+)\s+hundred(?:\s+(\w+))?', answer_lower)
            if hundred_match:
                base_word = hundred_match.group(1)
                extra_word = hundred_match.group(2)

                base_converted = convert_words_to_numbers(base_word)
                try:
                    base_number = float(base_converted) * 100
                    if extra_word:
                        extra_converted = convert_words_to_numbers(extra_word)
                        try:
                            extra_number = float(extra_converted)
                            result = int(base_number + extra_number)
                            return str(result)
                        except ValueError:
                            pass
                    return str(int(base_number))
                except ValueError:
                    pass

        # Handle zero cases
        if "zero" in answer_lower:
            return "0"

        # Try general word-to-number conversion as fallback
        converted = convert_words_to_numbers(answer)
        if converted != answer:
            # Check if it's a valid number
            try:
                float(converted)
                return converted
            except ValueError:
                pass

        # Remove $ and other non-numeric characters except decimal point
        clean_number = re.sub(r'[^\d.]', '', answer)
        if clean_number:
            try:
                # Validate it's a proper number
                float(clean_number)
                # For integers, return as string without decimal
                if '.' not in clean_number:
                    return clean_number
                else:
                    # For floats, return as string with decimal
                    return clean_number
            except ValueError:
                pass

        # No valid number found - return None to trigger LLM intervention
        return None
    
    def _normalize_date_answer(self, answer: str, field_id: str = "") -> Optional[str]:
        """Normalize date answers and format as MM/DD/YY using current month/year"""
        from utils import format_date_from_day

        # Check for references to previously collected dates
        answer_lower = answer.lower()
        if "same" in answer_lower and ("deposit" in answer_lower or "previous" in answer_lower):
            # User wants same date as deposit date
            if "deposit_date" in self.collected_answers:
                return self.collected_answers["deposit_date"]
            else:
                # No deposit date collected yet, can't resolve "same day"
                return None

        # First try to convert word to number
        converted = convert_words_to_numbers(answer)
        if converted != answer and converted.isdigit():
            day = int(converted)
            if 1 <= day <= 32:
                try:
                    # Format the day using current month/year
                    return format_date_from_day(day)
                except ValueError:
                    # If formatting fails, fall through to other methods
                    pass

        # Extract day number from answer with more flexible patterns
        # Try multiple patterns to catch various formats
        patterns = [
            r'\b([1-9]|[12][0-9]|3[0-2])\b',  # Basic number
            r'\b([1-9]|[12][0-9]|3[0-2])(?:st|nd|rd|th)\b',  # With ordinal suffix
            r'(?:day|date)\s+([1-9]|[12][0-9]|3[0-2])\b',  # "day 15" or "date 20"
            r'\b([1-9]|[12][0-9]|3[0-2])(?:\s*(?:st|nd|rd|th))?\s*(?:of|day|date)?\b'  # Flexible format
        ]

        for pattern in patterns:
            day_match = re.search(pattern, answer, re.IGNORECASE)
            if day_match:
                day = int(day_match.group(1))
                # Validate day is in valid range
                if 1 <= day <= 32:
                    try:
                        # Format the day using current month/year
                        return format_date_from_day(day)
                    except ValueError:
                        # If formatting fails, continue to next pattern
                        continue

        # No valid date found - return None to trigger LLM intervention
        return None
    
    def _normalize_yes_no_answer(self, answer: str) -> bool:
        """Normalize yes/no answers"""
        positive_words = ["yes", "yeah", "yep", "yup", "correct", "accurate", "all good", "right", "true"]
        return any(word in answer.lower() for word in positive_words)

    def _normalize_boolean_answer(self, answer: str) -> bool:
        """Normalize boolean answers for new_deposit question"""
        answer_lower = answer.lower().strip()

        # Words that indicate creating a new deposit (true)
        new_deposit_words = ["new", "creating", "create", "fresh", "start"]

        # Words that indicate adding to existing deposit (false)
        existing_deposit_words = ["existing", "add", "adding", "current", "old"]

        # Check for new deposit indicators
        if any(word in answer_lower for word in new_deposit_words):
            return True

        # Check for existing deposit indicators
        if any(word in answer_lower for word in existing_deposit_words):
            return False

        # Fallback to yes/no logic
        positive_words = ["yes", "yeah", "yep", "true"]
        negative_words = ["no", "nope", "false"]

        if any(word in answer_lower for word in positive_words):
            return True
        elif any(word in answer_lower for word in negative_words):
            return False

        # Default to True (new deposit) if unclear
        return True

    def _validate_100_bills_amount(self, answer: str) -> Optional[str]:
        """Validate amount in $100 bills (must be 0 or multiple of 100)"""
        answer_lower = answer.lower()

        # Handle zero cases
        if "zero" in answer_lower or answer.strip() == "0":
            return "0"

        # Handle "hundred" cases - both spoken words and digits
        if "hundred" in answer_lower:
            # First try to match digits + hundred (e.g., "4 hundred")
            digit_match = re.search(r'(\d+)\s*hundred', answer_lower)
            if digit_match:
                amount = int(digit_match.group(1)) * 100
                return str(amount)

            # Then try to match spoken words + hundred (e.g., "four hundred")
            word_match = re.search(r'(\w+)\s+hundred', answer_lower)
            if word_match:
                word = word_match.group(1)
                # Convert the word to number using our conversion function
                converted = convert_words_to_numbers(word)
                try:
                    base_number = int(converted)
                    amount = base_number * 100
                    # Validate it's a reasonable amount (1-9 hundreds)
                    if 1 <= base_number <= 9:
                        return str(amount)
                except ValueError:
                    pass

        # Extract numeric value for direct numeric input
        clean_number = re.sub(r'[^\d]', '', answer)
        if clean_number:
            amount = int(clean_number)
            # Check if it's 0 or multiple of 100
            if amount == 0 or amount % 100 == 0:
                return str(amount)

        return None  # Invalid amount

    def _format_next_question(self) -> str:
        """Format the next question with collected answers"""
        if self.current_question_index >= len(self.questions):
            return ""
        
        question = self.questions[self.current_question_index]
        question_text = question["question"]
        
        # Replace placeholders with collected answers
        for key, value in self.collected_answers.items():
            placeholder = f"{{{key}}}"
            if placeholder in question_text:
                question_text = question_text.replace(placeholder, str(value))
        
        return question_text
    
    def _initiate_llm_intervention(self, user_input: str, current_question: Dict) -> Tuple[str, bool, Dict]:
        """Initiate LLM intervention when user deviates"""
        self.chat_mode = ChatMode.LLM_INTERVENTION
        self.deviation_count += 1

        context = {
            "mode": "intervention",
            "agent_type": "intervention",
            "user_input": user_input,
            "current_question": current_question,
            "collected_answers": self.collected_answers,
            "questionnaire_progress": f"{self.current_question_index + 1}/{len(self.questions)}",
            "deviation_count": self.deviation_count,
            "intervention_context": self._get_intervention_context(user_input, current_question)
        }

        return "", True, context
    
    def _get_intervention_context(self, user_input: str, current_question: Dict) -> Dict:
        """Get context for intervention agent"""
        from datetime import datetime
        current_date = datetime.today().strftime("%m/%d/%y")

        return {
            "current_question": current_question['question'],
            "expected_type": current_question['type'].value,
            "user_input": user_input,
            "progress": f"{self.current_question_index + 1}/{len(self.questions)}",
            "current_date": current_date
        }

    def _get_final_validation_context(self) -> Dict:
        """Get context for final validation agent"""
        from datetime import datetime
        current_date = datetime.today().strftime("%m/%d/%y")

        return {
            "collected_answers": json.dumps(self.collected_answers, indent=2),
            "conversation_history": "",  # This will be filled by the calling function
            "current_date": current_date
        }
    
    def _complete_questionnaire(self) -> Tuple[str, bool, Dict]:
        """Complete the questionnaire using final validation agent"""
        # Switch to LLM intervention mode for final validation
        self.chat_mode = ChatMode.LLM_INTERVENTION

        # Create context for final validation agent
        context = {
            "mode": "final_validation",
            "agent_type": "final_validation",
            "current_question": None,  # No current question since all are completed
            "collected_answers": self.collected_answers,
            "questionnaire_progress": f"{len(self.questions)}/{len(self.questions)}",
            "deviation_count": self.deviation_count,
            "validation_context": self._get_final_validation_context()
        }

        # Return empty response with LLM needed flag to trigger final validation
        return "", True, context

    def prepare_form_data(self, answers: Dict) -> Dict:
        """Convert collected answers to form data format for fill_the_form_tool"""
        form_data = {
            "new_deposit": True,
            "reference_number": answers.get("reference_number", ""),
            "bank_code": answers.get("bank_code", ""),
            "customer_number": answers.get("customer_number", ""),
            "employee_number": answers.get("employee_number", ""),
            "pay_code": answers.get("pay_code", ""),
            "amount": answers.get("amount", ""),
            "account_number": answers.get("account_number", ""),
            "amount_in_100_bills": answers.get("amount_in_100_bills", "")
        }

        # Dates are already formatted as MM/DD/YY when collected, so use them directly
        form_data["deposit_date"] = answers.get("deposit_date", "")
        form_data["receipt_date"] = answers.get("receipt_date", "")

        return form_data

    def get_completion_message(self) -> str:
        """Get the final completion message after successful form submission"""
        return "🎉 **Success!** Your cash receipt has been successfully added to the system. Thank you for using the receipt entry system!"

    def handle_llm_response(self, llm_response: str, agent_result: Dict = None) -> Tuple[str, bool, Dict]:
        """
        Handle LLM response and check if questionnaire should resume or form should be filled

        Returns:
            Tuple[response_to_user, continue_with_llm, context]
        """
        # Check if the agent actually called fill_the_form_tool (more precise check)
        if agent_result and self._was_form_tool_called(agent_result):
            # Form tool was called, questionnaire is complete
            self.reset()  # Reset for next questionnaire
            return "🎉 **Success!** Your cash receipt has been successfully processed and added to the system. Thank you for using the receipt entry system!", False, {
                "mode": "form_completed",
                "form_result": agent_result
            }

        # Check if the validation agent completed the form (new detection method)
        if "Form filled successfully" in llm_response:
            # Form tool was called by validation agent, questionnaire is complete
            self.reset()  # Reset for next questionnaire
            return "🎉 **Success!** Your cash receipt has been successfully processed and added to the system. Thank you for using the receipt entry system!", False, {
                "mode": "form_completed",
                "validation_result": llm_response
            }

        if llm_response.startswith("QUESTIONNAIRE_ANSWER:"):
            # LLM extracted an answer, resume questionnaire
            answer_text = llm_response.replace("QUESTIONNAIRE_ANSWER:", "").strip()
            print(f"**********INTERVENTION EXTRACTED ANSWER: '{answer_text}'*************")

            # Only process if we have a current question (not in final validation)
            if self.current_question_index < len(self.questions):
                current_question = self.questions[self.current_question_index]

                # Validate and store the answer
                extracted_answer = self._extract_answer(answer_text, current_question)
                print(f"**********VALIDATED ANSWER: {extracted_answer}*************")
                if extracted_answer is not None:
                    self.collected_answers[current_question["id"]] = extracted_answer
                    self.current_question_index += 1
                    self.chat_mode = ChatMode.QUESTIONNAIRE
                    print(f"**********ANSWER STORED, ADVANCING TO QUESTION {self.current_question_index}*************")

                    # Continue with next question
                    if self.current_question_index >= len(self.questions):
                        response, needs_llm, context = self._complete_questionnaire()
                        return response, needs_llm, context
                    else:
                        next_question = self._format_next_question()
                        return next_question, False, {}
                else:
                    # Answer still not valid, continue with LLM
                    print(f"**********ANSWER VALIDATION FAILED FOR: '{answer_text}'*************")
                    return "I need a clearer answer. " + self._format_next_question(), True, {}
            else:
                # We're in final validation mode, continue with LLM
                return llm_response, True, {}
        else:
            # LLM is still handling the conversation, but let's try to extract an answer as fallback
            # This handles cases where the intervention agent doesn't use the exact format
            if self.current_question_index < len(self.questions):
                current_question = self.questions[self.current_question_index]

                # Try to extract answer from the LLM response directly
                extracted_answer = self._extract_answer(llm_response, current_question)
                if extracted_answer is not None:
                    print(f"**********FALLBACK EXTRACTION SUCCESSFUL: {extracted_answer}*************")
                    self.collected_answers[current_question["id"]] = extracted_answer
                    self.current_question_index += 1
                    self.chat_mode = ChatMode.QUESTIONNAIRE

                    # Continue with next question
                    if self.current_question_index >= len(self.questions):
                        response, needs_llm, context = self._complete_questionnaire()
                        return response, needs_llm, context
                    else:
                        next_question = self._format_next_question()
                        return next_question, False, {}

            # No answer found, continue with LLM
            return llm_response, True, {}

    def _was_form_tool_called(self, agent_result: Dict) -> bool:
        """Check if fill_the_form_tool was actually called by examining intermediate steps"""
        if not agent_result:
            return False

        # Check intermediate_steps for actual tool calls
        intermediate_steps = agent_result.get("intermediate_steps", [])
        if intermediate_steps:
            for step in intermediate_steps:
                if isinstance(step, tuple) and len(step) >= 2:
                    action = step[0]
                    # Check if the action involves fill_the_form_tool
                    if hasattr(action, 'tool') and action.tool == "fill_the_form_tool":
                        return True
                    elif isinstance(action, dict) and action.get('tool') == "fill_the_form_tool":
                        return True
                    elif "fill_the_form_tool" in str(action):
                        return True

        # Also check if the output explicitly mentions successful form submission
        output = agent_result.get("output", "")
        if "form filled successfully" in output.lower() or "receipt has been processed" in output.lower():
            return True

        return False

    def reset(self):
        """Reset questionnaire state"""
        self.current_question_index = 0
        self.chat_mode = ChatMode.QUESTIONNAIRE
        self.collected_answers = {}
        self.deviation_count = 0
    
    def get_first_question(self) -> str:
        """Get the first question to start the questionnaire"""
        if self.questions:
            return self.questions[0]["question"]
        return "Hello! Let's start with your receipt information."
    
    def is_questionnaire_complete(self) -> bool:
        """Check if questionnaire is complete"""
        return self.current_question_index >= len(self.questions)
    
    def get_progress(self) -> Dict:
        """Get current progress information"""
        return {
            "current_question": self.current_question_index + 1,
            "total_questions": len(self.questions),
            "mode": self.chat_mode.value,
            "collected_answers": self.collected_answers,
            "deviation_count": self.deviation_count
        }
