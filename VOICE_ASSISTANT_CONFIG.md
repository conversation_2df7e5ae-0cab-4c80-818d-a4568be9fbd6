# Voice Assistant Configuration Guide

This document describes all configurable options for the Voice Assistant applications.

## Project Structure

```
era-agents/
├── start_streaming_voice.py     # Main startup script
├── websocket_voice_server.py    # WebSocket server
├── run-streamlit-voice-streaming.py # Streaming UI
├── src/                          # Core modules
│   ├── __init__.py
│   ├── config_manager.py         # Configuration management
│   └── README.md
├── config/                       # Configuration files
│   ├── default.ini               # Default settings
│   ├── local.ini                # User overrides
│   └── README.md                # Config guide
├── agent/                        # AI agent modules
├── prompts/                      # Prompt templates
├── utils.py                      # Utility functions
└── VOICE_ASSISTANT_CONFIG.md     # This file
```

## Configuration Methods

The application uses INI configuration files located in the `config/` directory:

1. **`config/default.ini`** - Default configuration values (DO NOT EDIT)
2. **`config/local.ini`** - Local overrides and customizations (EDIT THIS FILE)

### Configuration Hierarchy

- Default values are loaded from `config/default.ini`
- Local customizations in `config/local.ini` override defaults
- If config files don't exist, they are automatically created on first run

## Configuration Sections

### [rag] - RAG (Retrieval-Augmented Generation) Configuration

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `prefix` | `"Which application should be used for the following inquiry:"` | Prefix text for RAG queries |
| `forms_path` | `"forms.json"` | Path to the forms configuration JSON file |
| `file_path` | `"ZI102.pdf"` | Path to the main document for RAG processing |
| `index_path` | `"rag_index"` | Directory path for storing the vector index |
| `model_name` | `"gpt-4o"` | LLM model name for chat and RAG operations |

### [audio] - Audio Configuration

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `rate` | `16000` | Audio sample rate in Hz for voice recording |
| `pause_threshold` | `1.0` | Pause threshold in seconds for voice recording |

### [transcription] - Transcription Configuration (English Only)

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `model` | `"gpt-4o-transcribe"` | Model to use for audio transcription |
| `language` | `"en"` | Fixed to English (hardcoded for consistency) |
| `temperature` | `0.0` | Controls randomness (0.0 = most deterministic, 1.0 = most random) |
| `prompt` | `"This is an English voice assistant conversation about business applications, forms, and data entry. Common terms include invoice, receipt, payment, cash, check, deposit, customer, vendor, account, form, application."` | Context prompt to guide English transcription accuracy |

### [ui] - UI Configuration

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `page_title` | `"ERA-IGNITE Voice Assistant"` | Browser page title |
| `layout` | `"wide"` | Streamlit page layout (`"wide"` or `"centered"`) |

**Note:** The page icon (🎙️) is hardcoded in the application to avoid encoding issues.

### [application] - Application Configuration

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `default_application` | `"Accounting"` | Default application name when JSON parsing fails |
| `default_form` | `"PRINT_CASH_RECEIPTS"` | Default form name when JSON parsing fails |

### [agent] - Agent Configuration

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `verbose` | `True` | Enable verbose logging for the agent executor |

### [tts] - TTS (Text-to-Speech) Configuration

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `enable_tts` | `True` | Enable/disable text-to-speech functionality |
| `autoplay_audio` | `True` | Enable/disable autoplay for audio responses |

### [session] - Session Configuration

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `enable_reset` | `True` | Enable/disable the session reset button |

## Example Configuration

### config/local.ini Example

```ini
# Local Voice Assistant Configuration
# This file overrides settings from default.ini

[rag]
model_name = gpt-3.5-turbo
forms_path = custom_forms.json

[audio]
rate = 22050
pause_threshold = 1.5

[transcription]
temperature = 0.1
prompt = This is an English voice assistant for business applications with specialized accounting terminology.

[ui]
page_title = My Custom Voice Assistant
layout = centered

[application]
default_application = Sales
default_form = CREATE_INVOICE

[agent]
verbose = False

[tts]
enable_tts = True
autoplay_audio = False

[session]
enable_reset = True
```

### Quick Start

1. **Run the application** - Configuration files are loaded automatically
2. **Edit `config/local.ini`** - Add only the settings you want to override
3. **Restart the application** - Changes take effect on restart

### How It Works

- The application loads `config/default.ini` first
- Then loads `config/local.ini` which automatically overrides any matching settings
- You only need to add settings to `local.ini` that you want to change
- No need to uncomment - just add the section and setting directly

## Configuration Tips

1. **Model Selection**: Choose appropriate models based on your needs:
   - `gpt-4o`: Best quality, higher cost
   - `gpt-3.5-turbo`: Good balance of quality and cost
   - `claude-3-sonnet`: Alternative high-quality option

2. **Audio Quality**: Higher `AUDIO_RATE` values provide better audio quality but larger file sizes:
   - `8000`: Phone quality
   - `16000`: Standard quality (recommended)
   - `22050`: CD quality
   - `44100`: High quality

3. **Performance**: Set `AGENT_VERBOSE=False` in production to reduce logging overhead

4. **Accessibility**: Set `AUTOPLAY_AUDIO=False` if autoplay causes issues in your browser

5. **File Paths**: Use absolute paths if running from different directories

## Troubleshooting

- **File Not Found Errors**: Check that `FORMS_PATH` and `FILE_PATH` point to existing files
- **Audio Issues**: Verify `AUDIO_RATE` is supported by your browser/system
- **Model Errors**: Ensure `MODEL_NAME` is available in your LiteLLM configuration
- **Index Issues**: Delete and recreate the `INDEX_PATH` directory if vector store is corrupted
