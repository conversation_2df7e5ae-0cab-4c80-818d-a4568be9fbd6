# Streaming Voice Assistant

A continuous voice chat interface that uses WebSockets for real-time audio streaming, eliminating the need to press buttons during conversation.

## 🚀 Quick Start

### Option 1: Automatic Startup (Recommended)
```bash
python start_streaming_voice.py
```

### Option 2: Manual Startup
1. **Start WebSocket Server:**
   ```bash
   python websocket_voice_server.py
   ```

2. **Start Streamlit App (in another terminal):**
   ```bash
   streamlit run run-streamlit-voice-streaming.py --server.port 8503
   ```

3. **Open your browser to:** `http://localhost:8503`

## 🎙️ How It Works

### Architecture
- **WebSocket Server** (`websocket_voice_server.py`): Handles real-time audio processing
- **Streamlit App** (`run-streamlit-voice-streaming.py`): Provides the user interface
- **HTML Component** (`voice_streaming_component.html`): Captures browser audio
- **Voice Processing**: Real-time transcription and voice activity detection
- **Questionnaire System**: Structured 10-question flow with minimal LLM usage
- **LLM Agents**: Two specialized agents for intervention and final validation

### Features
- 🔴 **Continuous Streaming**: Always listening, no button presses needed
- 🎯 **Smart Voice Detection**: Automatically detects when you start/stop speaking
- ⚡ **Real-time Processing**: Instant transcription and responses
- 🔊 **Audio Visualization**: Live audio level monitoring
- 🗣️ **Natural Conversation**: Flows like talking to a person
- 📋 **Structured Questionnaire**: Guided 10-question cash receipt flow
- 🤖 **Smart LLM Integration**: Minimal AI usage with targeted intervention
- ✅ **Intelligent Validation**: Auto-completion and form submission

## 🛠️ Configuration

The streaming voice assistant uses the same configuration as the regular voice assistant:

### Audio Settings (`config/local.ini`)
```ini
[audio]
rate = 16000
pause_threshold = 1.0

[streaming]
silence_threshold = 0.01
max_silence_duration = 2.0
min_audio_duration = 1.0
```

### Streaming Parameters
- **`silence_threshold`**: RMS level below which audio is considered silence
- **`max_silence_duration`**: Seconds of silence before stopping recording
- **`min_audio_duration`**: Minimum seconds of audio required for processing

## 🔧 Technical Details

### WebSocket Protocol
The client-server communication uses JSON messages:

**Client to Server:**
```json
{
  "type": "audio_chunk",
  "audio_data": "base64_encoded_audio"
}
```

**Server to Client:**
```json
{
  "status": "transcribed",
  "text": "transcribed speech",
  "duration": 2.5
}
```

### Browser Requirements
- Modern browser with WebRTC support
- Microphone access permissions
- WebSocket support

## 📋 Questionnaire System

The streaming voice assistant uses a structured questionnaire approach for cash receipt processing:

### How It Works
1. **Structured Flow**: 10 predefined questions guide users through cash receipt creation
2. **Rule-Based Validation**: Fast validation for expected answer formats
3. **LLM Intervention**: AI assistance only when users deviate or need help
4. **Final Validation**: Intelligent review and auto-completion before form submission

### LLM Agents
- **Intervention Agent** (`LLM_INTERVENTION_AGENT`): Handles user questions and deviations
- **Final Validation Agent** (`FINAL_VALIDATION_AGENT`): Validates and submits completed forms

### Benefits
- ⚡ **Fast Processing**: Minimal AI usage for quick responses
- 🎯 **Guided Experience**: Clear question sequence
- 🤖 **Smart Help**: AI assistance when needed
- ✅ **Reliable Completion**: Intelligent validation ensures form accuracy

## 🚨 Troubleshooting

### Common Issues

1. **"WebSocket connection failed"**
   - Ensure `websocket_voice_server.py` is running
   - Check if port 8765 is available

2. **"Microphone access denied"**
   - Allow microphone permissions in your browser
   - Check browser security settings

3. **"No audio detected"**
   - Adjust `silence_threshold` in configuration
   - Check microphone levels
   - Ensure microphone is not muted

4. **"Transcription not working"**
   - Verify OpenAI API key is set
   - Check internet connection
   - Review transcription model settings

### Debug Mode
Enable debug logging by setting environment variable:
```bash
export PYTHONPATH=.
export LOG_LEVEL=DEBUG
python websocket_voice_server.py
```

## 📝 Usage Tips

1. **Start Streaming**: Click "Start Streaming" and allow microphone access
2. **Speak Naturally**: Talk normally, pauses are automatically detected
3. **Visual Feedback**: Watch the audio level bar and status indicator
4. **Continuous Chat**: No need to press buttons between messages
5. **Stop Anytime**: Click "Stop Streaming" to end the session

## 🔄 Comparison with Button Mode

| Feature | Button Mode | Streaming Mode |
|---------|-------------|----------------|
| User Action | Press button for each message | Speak continuously |
| Response Time | Manual trigger | Automatic detection |
| Conversation Flow | Interrupted | Natural |
| Audio Processing | On-demand | Real-time |
| User Experience | Click → Speak → Wait | Just speak |

## 🎯 Next Steps

The streaming voice assistant provides a foundation for:
- Real-time voice commands
- Hands-free operation
- Voice-controlled applications
- Continuous conversation interfaces

Enjoy your hands-free voice assistant experience! 🎙️✨
