"""
Streaming Voice Assistant Streamlit Application

This application provides a continuous voice chat interface using WebSockets
for real-time audio streaming, eliminating the need to press buttons.
"""

import re
import json
import streamlit as st
import streamlit.components.v1 as components
import base64
import tempfile

from langchain_community.chat_models import ChatLiteLLM
from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.prompts import PromptTemplate

from agent.agents import RAGChain
from utils import index_exists, get_tools, create_tts_response
from src.config_manager import VoiceAssistantConfig

@st.cache_data(show_spinner=False)
def cached_tts(text: str) -> bytes:
    """Cache TTS responses for better performance"""
    return create_tts_response(text)

# Initialize configuration
config = VoiceAssistantConfig()

# Validate configuration and show warnings if needed
config_issues = config.validate_config()
if config_issues:
    st.error("⚠️ Configuration Issues Found:")
    for key, issue in config_issues.items():
        st.error(f"- {key}: {issue}")
    st.stop()

# Set page config for voice assistant
st.set_page_config(
    page_title=f"🎙️ {config.page_title} - Streaming",
    page_icon=config.page_icon,
    layout=config.layout
)

# Init session state
if "conversation_history" not in st.session_state:
    st.session_state.conversation_history = []
if "agent_executor" not in st.session_state:
    st.session_state.agent_executor = None
if "inputs" not in st.session_state:
    st.session_state.inputs = {}
if "audio_responses" not in st.session_state:
    st.session_state.audio_responses = {}
if "streaming_initialized" not in st.session_state:
    st.session_state.streaming_initialized = False
if "processing_voice" not in st.session_state:
    st.session_state.processing_voice = False

# Sidebar config
st.sidebar.title("🎙️ Streaming Voice Assistant")
st.sidebar.markdown("---")

# Configuration display
with st.sidebar.expander("⚙️ Current Configuration", expanded=False):
    config_info = config.get_config_info()
    st.write("**Configuration Files:**")
    st.write(f"📁 Config Directory: `{config_info['config_dir']}`")
    st.write(f"📄 Default Config: {'✅' if config_info['default_exists'] else '❌'}")
    st.write(f"📝 Local Config: {'✅' if config_info['local_exists'] else '❌'}")
    
    st.write("**Current Settings:**")
    st.json({
        "Model": config.model_name,
        "Audio Rate": f"{config.audio_rate} Hz",
        "TTS Enabled": config.enable_tts,
        "Autoplay": config.autoplay_audio,
        "Forms Path": config.forms_path,
        "Document": config.file_path
    })
    
    st.info("💡 Edit `config/local.ini` to customize settings")

# Initialize streaming mode
if not st.session_state.streaming_initialized:
    st.session_state.streaming_initialized = True

# Show current session info
st.sidebar.subheader("📊 Current Session")
st.sidebar.info(f"**Messages:** {len(st.session_state.conversation_history)}")
if config.enable_session_reset and st.sidebar.button("🔄 Reset Session", type="secondary"):
    # Reset conversation but keep agent
    st.session_state.conversation_history = []
    st.session_state.audio_responses = {}
    st.session_state.processing_voice = False
    st.rerun()

# Main Voice Chat Interface
st.title(f"{config.page_icon} {config.page_title} - Streaming Mode")
st.markdown("**ERA-IGNITE AI Assistant**")

# Load and display the voice streaming component
with open("voice_streaming_component.html", "r", encoding="utf-8") as f:
    html_content = f.read()

# Embed the HTML component - this now handles everything!
st.subheader("🎙️ Beginning of Voice Chat")
st.markdown("**Speak naturally and get instant AI help in filling out any form!**")

voice_component = components.html(html_content, height=650, scrolling=False)

# Welcome instructions
st.markdown("""
## 🚀 How to Use:

1. 🎙️ **Click "Start Streaming"** above to begin continuous voice chat
2. 🗣️ **Speak naturally** - the system automatically detects when you start and stop talking
3. 🤖 **Get instant AI responses** - displayed in real-time in the chat area above
4. ⏹️ **Click "Stop Streaming"** when you're done

### ✨ Features:
- 🔄 **True Continuous Streaming** - No button presses needed during conversation
- 🎯 **Voice Activity Detection** - Automatically detects speech and silence
- 📊 **Real-time Audio Levels** - Visual feedback while speaking
- 🤖 **AI-Powered Assistant** - Intelligent responses to your queries
- 💬 **Live Chat Display** - See conversation in real-time

**The entire conversation happens in the streaming interface above - no need to scroll down!**
""")


