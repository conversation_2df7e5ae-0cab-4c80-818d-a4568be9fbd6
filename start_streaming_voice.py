"""
Startup script for the streaming voice assistant

This script starts both the WebSocket server and Streamlit app.
"""

import subprocess
import time
import threading
import sys
import os

def start_websocket_server():
    """Start the WebSocket voice server"""
    print("🔌 Starting WebSocket voice server...")
    try:
        subprocess.run([sys.executable, "websocket_voice_server.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting WebSocket server: {e}")
    except KeyboardInterrupt:
        print("🛑 WebSocket server stopped by user")

def start_streamlit_app():
    """Start the Streamlit application"""
    print("🚀 Starting Streamlit voice assistant...")
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "run-streamlit-voice-streaming.py",
            "--server.port", "8503",
            "--server.headless", "true"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting Streamlit app: {e}")
    except KeyboardInterrupt:
        print("🛑 Streamlit app stopped by user")

def main():
    """Main function to start both services"""
    print("🎙️ Starting Streaming Voice Assistant...")
    print("=" * 50)
    
    # Start WebSocket server in a separate thread
    websocket_thread = threading.Thread(target=start_websocket_server, daemon=True)
    websocket_thread.start()
    
    # Wait a moment for WebSocket server to start
    time.sleep(2)
    
    # Start Streamlit app in main thread
    try:
        start_streamlit_app()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down streaming voice assistant...")
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
